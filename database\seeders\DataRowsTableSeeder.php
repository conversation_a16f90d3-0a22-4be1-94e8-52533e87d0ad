<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DataRowsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('data_rows')->delete();
        
        \DB::table('data_rows')->insert(array (
            0 => 
            array (
                'id' => 1,
                'data_type_id' => 1,
                'field' => 'id',
                'type' => 'number',
                'display_name' => 'ID',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            1 => 
            array (
                'id' => 2,
                'data_type_id' => 1,
                'field' => 'name',
                'type' => 'text',
                'display_name' => 'Name',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            2 => 
            array (
                'id' => 3,
                'data_type_id' => 1,
                'field' => 'email',
                'type' => 'text',
                'display_name' => 'Email',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 4,
            ),
            3 => 
            array (
                'id' => 4,
                'data_type_id' => 1,
                'field' => 'password',
                'type' => 'password',
                'display_name' => 'Password',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 1,
                'add' => 1,
                'delete' => 0,
                'details' => '{}',
                'order' => 5,
            ),
            4 => 
            array (
                'id' => 5,
                'data_type_id' => 1,
                'field' => 'remember_token',
                'type' => 'text',
                'display_name' => 'Remember Token',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 8,
            ),
            5 => 
            array (
                'id' => 6,
                'data_type_id' => 1,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 38,
            ),
            6 => 
            array (
                'id' => 7,
                'data_type_id' => 1,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 34,
            ),
            7 => 
            array (
                'id' => 8,
                'data_type_id' => 1,
                'field' => 'avatar',
                'type' => 'image',
                'display_name' => 'Avatar',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 2,
            ),
            8 => 
            array (
                'id' => 9,
                'data_type_id' => 1,
                'field' => 'user_belongsto_role_relationship',
                'type' => 'relationship',
                'display_name' => 'Role',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 0,
                'details' => '{"model":"TCG\\\\Voyager\\\\Models\\\\Role","table":"roles","type":"belongsTo","column":"role_id","key":"id","label":"display_name","pivot_table":"roles","pivot":"0","taggable":"0"}',
                'order' => 17,
            ),
            9 => 
            array (
                'id' => 10,
                'data_type_id' => 1,
                'field' => 'user_belongstomany_role_relationship',
                'type' => 'relationship',
                'display_name' => 'Roles',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{"model":"TCG\\\\Voyager\\\\Models\\\\Role","table":"roles","type":"belongsToMany","column":"id","key":"id","label":"display_name","pivot_table":"user_roles","pivot":"1","taggable":"0"}',
                'order' => 18,
            ),
            10 => 
            array (
                'id' => 11,
                'data_type_id' => 1,
                'field' => 'settings',
                'type' => 'hidden',
                'display_name' => 'Settings',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 19,
            ),
            11 => 
            array (
                'id' => 12,
                'data_type_id' => 2,
                'field' => 'id',
                'type' => 'number',
                'display_name' => 'ID',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => NULL,
                'order' => 1,
            ),
            12 => 
            array (
                'id' => 13,
                'data_type_id' => 2,
                'field' => 'name',
                'type' => 'text',
                'display_name' => 'Name',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => NULL,
                'order' => 2,
            ),
            13 => 
            array (
                'id' => 14,
                'data_type_id' => 2,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => NULL,
                'order' => 3,
            ),
            14 => 
            array (
                'id' => 15,
                'data_type_id' => 2,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => NULL,
                'order' => 4,
            ),
            15 => 
            array (
                'id' => 16,
                'data_type_id' => 3,
                'field' => 'id',
                'type' => 'number',
                'display_name' => 'ID',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            16 => 
            array (
                'id' => 17,
                'data_type_id' => 3,
                'field' => 'name',
                'type' => 'text',
                'display_name' => 'Name',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 2,
            ),
            17 => 
            array (
                'id' => 18,
                'data_type_id' => 3,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 3,
            ),
            18 => 
            array (
                'id' => 19,
                'data_type_id' => 3,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 4,
            ),
            19 => 
            array (
                'id' => 20,
                'data_type_id' => 3,
                'field' => 'display_name',
                'type' => 'text',
                'display_name' => 'Display Name',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            20 => 
            array (
                'id' => 21,
                'data_type_id' => 1,
                'field' => 'role_id',
                'type' => 'text',
                'display_name' => 'Role',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 6,
            ),
            21 => 
            array (
                'id' => 22,
                'data_type_id' => 4,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"description":"Unique Wallet Identifier. If adding a new entry from admin panel, entry a random, unique string."}',
                'order' => 1,
            ),
            22 => 
            array (
                'id' => 23,
                'data_type_id' => 4,
                'field' => 'user_id',
                'type' => 'text',
                'display_name' => 'User Id',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            23 => 
            array (
                'id' => 24,
                'data_type_id' => 4,
                'field' => 'total',
                'type' => 'text',
                'display_name' => 'Total',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            24 => 
            array (
                'id' => 27,
                'data_type_id' => 4,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 6,
            ),
            25 => 
            array (
                'id' => 28,
                'data_type_id' => 4,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 7,
            ),
            26 => 
            array (
                'id' => 29,
                'data_type_id' => 6,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'UUID',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 1,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            27 => 
            array (
                'id' => 30,
                'data_type_id' => 6,
                'field' => 'filename',
                'type' => 'text',
                'display_name' => 'Filename',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 9,
            ),
            28 => 
            array (
                'id' => 31,
                'data_type_id' => 6,
                'field' => 'driver',
                'type' => 'select_dropdown',
                'display_name' => 'Driver',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"0","options":{"0":"Local","1":"S3","2":"Wasabi","3":"DigitalOcean","4":"Minio","5":"Pushr"}}',
                'order' => 7,
            ),
            29 => 
            array (
                'id' => 32,
                'data_type_id' => 6,
                'field' => 'type',
                'type' => 'text',
                'display_name' => 'Type',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 8,
            ),
            30 => 
            array (
                'id' => 33,
                'data_type_id' => 6,
                'field' => 'user_id',
                'type' => 'text',
                'display_name' => 'User Id',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            31 => 
            array (
                'id' => 34,
                'data_type_id' => 6,
                'field' => 'post_id',
                'type' => 'text',
                'display_name' => 'Post Id',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 4,
            ),
            32 => 
            array (
                'id' => 35,
                'data_type_id' => 6,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 11,
            ),
            33 => 
            array (
                'id' => 36,
                'data_type_id' => 6,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 13,
            ),
            34 => 
            array (
                'id' => 37,
                'data_type_id' => 9,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'UUID',
                'required' => 1,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"description":"Unique Wallet Identifier. If adding a new entry from admin panel, entry a random, unique string."}',
                'order' => 1,
            ),
            35 => 
            array (
                'id' => 38,
                'data_type_id' => 9,
                'field' => 'from_user_id',
                'type' => 'text',
                'display_name' => 'From User Id',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            36 => 
            array (
                'id' => 39,
                'data_type_id' => 9,
                'field' => 'to_user_id',
                'type' => 'text',
                'display_name' => 'To User Id',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            37 => 
            array (
                'id' => 40,
                'data_type_id' => 9,
                'field' => 'type',
                'type' => 'select_dropdown',
                'display_name' => 'Type',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"tip","options":{"tip":"Tip","reaction":"Reaction","new-comment":"Comment","new-subscription":"Subscription","withdrawal-action":"Withdrawal","new-message":"Message"}}',
                'order' => 11,
            ),
            38 => 
            array (
                'id' => 41,
                'data_type_id' => 9,
                'field' => 'post_id',
                'type' => 'text',
                'display_name' => 'Post Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 6,
            ),
            39 => 
            array (
                'id' => 42,
                'data_type_id' => 9,
                'field' => 'post_comment_id',
                'type' => 'text',
                'display_name' => 'Post Comment Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 7,
            ),
            40 => 
            array (
                'id' => 43,
                'data_type_id' => 9,
                'field' => 'subscription_id',
                'type' => 'text',
                'display_name' => 'Subscription Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 8,
            ),
            41 => 
            array (
                'id' => 44,
                'data_type_id' => 9,
                'field' => 'transaction_id',
                'type' => 'text',
                'display_name' => 'Transaction Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 9,
            ),
            42 => 
            array (
                'id' => 45,
                'data_type_id' => 9,
                'field' => 'reaction_id',
                'type' => 'text',
                'display_name' => 'Reaction Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 10,
            ),
            43 => 
            array (
                'id' => 47,
                'data_type_id' => 9,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 16,
            ),
            44 => 
            array (
                'id' => 48,
                'data_type_id' => 9,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 17,
            ),
            45 => 
            array (
                'id' => 49,
                'data_type_id' => 10,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            46 => 
            array (
                'id' => 50,
                'data_type_id' => 10,
                'field' => 'user_id',
                'type' => 'text',
                'display_name' => 'User Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            47 => 
            array (
                'id' => 51,
                'data_type_id' => 10,
                'field' => 'post_id',
                'type' => 'text',
                'display_name' => 'Post Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 4,
            ),
            48 => 
            array (
                'id' => 52,
                'data_type_id' => 10,
                'field' => 'message',
                'type' => 'text',
                'display_name' => 'Message',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            49 => 
            array (
                'id' => 53,
                'data_type_id' => 10,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 6,
            ),
            50 => 
            array (
                'id' => 54,
                'data_type_id' => 10,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 7,
            ),
            51 => 
            array (
                'id' => 55,
                'data_type_id' => 11,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            52 => 
            array (
                'id' => 56,
                'data_type_id' => 11,
                'field' => 'user_id',
                'type' => 'text',
                'display_name' => 'User Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            53 => 
            array (
                'id' => 57,
                'data_type_id' => 11,
                'field' => 'text',
                'type' => 'text',
                'display_name' => 'Text',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            54 => 
            array (
                'id' => 58,
                'data_type_id' => 11,
                'field' => 'status',
                'type' => 'select_dropdown',
                'display_name' => 'Status',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"requested","options":{"0":"Pending","1":"Approved","2":"Disapproved"}}',
                'order' => 6,
            ),
            55 => 
            array (
                'id' => 59,
                'data_type_id' => 11,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 11,
            ),
            56 => 
            array (
                'id' => 60,
                'data_type_id' => 11,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 10,
            ),
            57 => 
            array (
                'id' => 61,
                'data_type_id' => 12,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            58 => 
            array (
                'id' => 62,
                'data_type_id' => 12,
                'field' => 'user_id',
                'type' => 'text',
                'display_name' => 'User Id',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            59 => 
            array (
                'id' => 63,
                'data_type_id' => 12,
                'field' => 'post_id',
                'type' => 'text',
                'display_name' => 'Post Id',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 4,
            ),
            60 => 
            array (
                'id' => 64,
                'data_type_id' => 12,
                'field' => 'post_comment_id',
                'type' => 'text',
                'display_name' => 'Post Comment Id',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            61 => 
            array (
                'id' => 65,
                'data_type_id' => 12,
                'field' => 'reaction_type',
                'type' => 'select_dropdown',
                'display_name' => 'Reaction Type',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"like","options":{"like":"Like"}}',
                'order' => 6,
            ),
            62 => 
            array (
                'id' => 66,
                'data_type_id' => 12,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 7,
            ),
            63 => 
            array (
                'id' => 67,
                'data_type_id' => 12,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 8,
            ),
            64 => 
            array (
                'id' => 68,
                'data_type_id' => 13,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            65 => 
            array (
                'id' => 69,
                'data_type_id' => 13,
                'field' => 'sender_user_id',
                'type' => 'text',
                'display_name' => 'SubscriberID',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            66 => 
            array (
                'id' => 70,
                'data_type_id' => 13,
                'field' => 'recipient_user_id',
                'type' => 'text',
                'display_name' => 'CreatorID',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            67 => 
            array (
                'id' => 71,
                'data_type_id' => 13,
                'field' => 'paypal_agreement_id',
                'type' => 'text',
                'display_name' => 'Paypal ID',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 10,
            ),
            68 => 
            array (
                'id' => 72,
                'data_type_id' => 13,
                'field' => 'stripe_subscription_id',
                'type' => 'text',
                'display_name' => 'Stripe ID',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 13,
            ),
            69 => 
            array (
                'id' => 73,
                'data_type_id' => 13,
                'field' => 'paypal_plan_id',
                'type' => 'text',
                'display_name' => 'Paypal Plan ID',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 11,
            ),
            70 => 
            array (
                'id' => 74,
                'data_type_id' => 13,
                'field' => 'type',
                'type' => 'select_dropdown',
                'display_name' => 'Type',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"one-month-subscription","options":{"one-month-subscription":"One month sub","three-months-subscription":"Three months sub","six-months-subscription":"Six months sub","yearly-subscription":"Yearly sub"}}',
                'order' => 7,
            ),
            71 => 
            array (
                'id' => 75,
                'data_type_id' => 13,
                'field' => 'status',
                'type' => 'select_dropdown',
                'display_name' => 'Status',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"pending","options":{"pending":"Pending","completed":"Completed","suspended":"Suspended","update-needed":"Update needed","canceled":"Canceled","expired":"Expired"}}',
                'order' => 6,
            ),
            72 => 
            array (
                'id' => 76,
                'data_type_id' => 13,
                'field' => 'expires_at',
                'type' => 'timestamp',
                'display_name' => 'Expires At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 14,
            ),
            73 => 
            array (
                'id' => 77,
                'data_type_id' => 13,
                'field' => 'canceled_at',
                'type' => 'timestamp',
                'display_name' => 'Canceled At',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 15,
            ),
            74 => 
            array (
                'id' => 78,
                'data_type_id' => 13,
                'field' => 'amount',
                'type' => 'text',
                'display_name' => 'Amount',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 9,
            ),
            75 => 
            array (
                'id' => 79,
                'data_type_id' => 13,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 16,
            ),
            76 => 
            array (
                'id' => 80,
                'data_type_id' => 13,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 17,
            ),
            77 => 
            array (
                'id' => 81,
                'data_type_id' => 14,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 1,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            78 => 
            array (
                'id' => 82,
                'data_type_id' => 14,
                'field' => 'sender_user_id',
                'type' => 'text',
                'display_name' => 'SenderID',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            79 => 
            array (
                'id' => 83,
                'data_type_id' => 14,
                'field' => 'recipient_user_id',
                'type' => 'text',
                'display_name' => 'RecipientID',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            80 => 
            array (
                'id' => 84,
                'data_type_id' => 14,
                'field' => 'subscription_id',
                'type' => 'text',
                'display_name' => 'SubscriptionID',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 10,
            ),
            81 => 
            array (
                'id' => 85,
                'data_type_id' => 14,
                'field' => 'post_id',
                'type' => 'text',
                'display_name' => 'PostID',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 11,
            ),
            82 => 
            array (
                'id' => 86,
                'data_type_id' => 14,
                'field' => 'stripe_transaction_id',
                'type' => 'text',
                'display_name' => 'StripeTransactionID',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 12,
            ),
            83 => 
            array (
                'id' => 87,
                'data_type_id' => 14,
                'field' => 'stripe_session_id',
                'type' => 'text',
                'display_name' => 'StripeSessionID',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 15,
            ),
            84 => 
            array (
                'id' => 88,
                'data_type_id' => 14,
                'field' => 'paypal_transaction_id',
                'type' => 'text',
                'display_name' => 'PaypalTransactionID',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 16,
            ),
            85 => 
            array (
                'id' => 89,
                'data_type_id' => 14,
                'field' => 'paypal_transaction_token',
                'type' => 'text',
                'display_name' => 'PaypalTransactionToken',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 18,
            ),
            86 => 
            array (
                'id' => 90,
                'data_type_id' => 14,
                'field' => 'status',
                'type' => 'select_dropdown',
                'display_name' => 'Status',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"pending","options":{"pending":"Pending","canceled":"Canceled","approved":"Approved","declined":"Declined","refunded":"Refunded","initiated":"Initiated","partially-paid":"Partially paid"}}',
                'order' => 6,
            ),
            87 => 
            array (
                'id' => 91,
                'data_type_id' => 14,
                'field' => 'type',
                'type' => 'select_dropdown',
                'display_name' => 'Type',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"tip","options":{"tip":"Post Tip","chat-tip":"Chat tip","post-unlock":"Post unlock","message-unlock":"Message unlock","deposit":"Deposit","withdrawal":"Withdrawal","one-month-subscription":"One month sub","three-months-subscription":"Three months sub","six-months-subscription":"Six months sub","yearly-subscription":"Yearly sub","subscription-renewal":"Subscription renewal","stream-access":"Stream unlock"}}',
                'order' => 7,
            ),
            88 => 
            array (
                'id' => 92,
                'data_type_id' => 14,
                'field' => 'payment_provider',
                'type' => 'select_dropdown',
                'display_name' => 'Payment Provider',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"credit","options":{"stripe":"Stripe","paypal":"Paypal","ccbill":"CCBill","coinbase":"Coinbase","nowpayments":"Nowpayments","paystack":"Paystack","oxxo":"Oxxo","credit":"Credit"}}',
                'order' => 8,
            ),
            89 => 
            array (
                'id' => 93,
                'data_type_id' => 14,
                'field' => 'currency',
                'type' => 'text',
                'display_name' => 'Currency',
                'required' => 1,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 21,
            ),
            90 => 
            array (
                'id' => 94,
                'data_type_id' => 14,
                'field' => 'paypal_payer_id',
                'type' => 'text',
                'display_name' => 'PaypalPayerId',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 19,
            ),
            91 => 
            array (
                'id' => 95,
                'data_type_id' => 14,
                'field' => 'amount',
                'type' => 'text',
                'display_name' => 'Amount',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 9,
            ),
            92 => 
            array (
                'id' => 96,
                'data_type_id' => 14,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 28,
            ),
            93 => 
            array (
                'id' => 97,
                'data_type_id' => 14,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 30,
            ),
            94 => 
            array (
                'id' => 98,
                'data_type_id' => 15,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            95 => 
            array (
                'id' => 99,
                'data_type_id' => 15,
                'field' => 'user_id',
                'type' => 'text',
                'display_name' => 'User Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            96 => 
            array (
                'id' => 100,
                'data_type_id' => 15,
                'field' => 'post_id',
                'type' => 'text',
                'display_name' => 'Post Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 4,
            ),
            97 => 
            array (
                'id' => 101,
                'data_type_id' => 15,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            98 => 
            array (
                'id' => 102,
                'data_type_id' => 15,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 6,
            ),
            99 => 
            array (
                'id' => 103,
                'data_type_id' => 16,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 1,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            100 => 
            array (
                'id' => 104,
                'data_type_id' => 16,
                'field' => 'name',
                'type' => 'text',
                'display_name' => 'Name',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 4,
            ),
            101 => 
            array (
                'id' => 105,
                'data_type_id' => 16,
                'field' => 'user_id',
                'type' => 'text',
                'display_name' => 'User Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            102 => 
            array (
                'id' => 106,
                'data_type_id' => 16,
                'field' => 'type',
                'type' => 'select_dropdown',
                'display_name' => 'Type',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"following","options":{"following":"Following","blocked":"Blocked","custom":"Custom"}}',
                'order' => 5,
            ),
            103 => 
            array (
                'id' => 107,
                'data_type_id' => 16,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 6,
            ),
            104 => 
            array (
                'id' => 108,
                'data_type_id' => 16,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 7,
            ),
            105 => 
            array (
                'id' => 109,
                'data_type_id' => 17,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            106 => 
            array (
                'id' => 110,
                'data_type_id' => 17,
                'field' => 'list_id',
                'type' => 'text',
                'display_name' => 'List Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 2,
            ),
            107 => 
            array (
                'id' => 111,
                'data_type_id' => 17,
                'field' => 'user_id',
                'type' => 'text',
                'display_name' => 'User Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            108 => 
            array (
                'id' => 112,
                'data_type_id' => 17,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 6,
            ),
            109 => 
            array (
                'id' => 113,
                'data_type_id' => 17,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 7,
            ),
            110 => 
            array (
                'id' => 114,
                'data_type_id' => 18,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            111 => 
            array (
                'id' => 115,
                'data_type_id' => 18,
                'field' => 'sender_id',
                'type' => 'text',
                'display_name' => 'Sender Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            112 => 
            array (
                'id' => 116,
                'data_type_id' => 18,
                'field' => 'receiver_id',
                'type' => 'text',
                'display_name' => 'Receiver Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            113 => 
            array (
                'id' => 117,
                'data_type_id' => 18,
                'field' => 'replyTo',
                'type' => 'text',
                'display_name' => 'ReplyTo',
                'required' => 1,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 6,
            ),
            114 => 
            array (
                'id' => 118,
                'data_type_id' => 18,
                'field' => 'message',
                'type' => 'text',
                'display_name' => 'Message',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 8,
            ),
            115 => 
            array (
                'id' => 119,
                'data_type_id' => 18,
                'field' => 'isSeen',
                'type' => 'checkbox',
                'display_name' => 'IsSeen',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"True","off":"False","checked":"true"}',
                'order' => 9,
            ),
            116 => 
            array (
                'id' => 120,
                'data_type_id' => 18,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 10,
            ),
            117 => 
            array (
                'id' => 121,
                'data_type_id' => 18,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 11,
            ),
            118 => 
            array (
                'id' => 122,
                'data_type_id' => 19,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            119 => 
            array (
                'id' => 123,
                'data_type_id' => 19,
                'field' => 'user_id',
                'type' => 'text',
                'display_name' => 'User Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            120 => 
            array (
                'id' => 124,
                'data_type_id' => 19,
                'field' => 'amount',
                'type' => 'text',
                'display_name' => 'Amount',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 4,
            ),
            121 => 
            array (
                'id' => 125,
                'data_type_id' => 19,
                'field' => 'status',
                'type' => 'select_dropdown',
                'display_name' => 'Status',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"requested","options":{"requested":"Requested","rejected":"Rejected","approved":"Approved"}}',
                'order' => 6,
            ),
            122 => 
            array (
                'id' => 126,
                'data_type_id' => 19,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 12,
            ),
            123 => 
            array (
                'id' => 127,
                'data_type_id' => 19,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 9,
            ),
            124 => 
            array (
                'id' => 128,
                'data_type_id' => 20,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            125 => 
            array (
                'id' => 129,
                'data_type_id' => 20,
                'field' => 'name',
                'type' => 'text',
                'display_name' => 'Name',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 2,
            ),
            126 => 
            array (
                'id' => 130,
                'data_type_id' => 20,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            127 => 
            array (
                'id' => 131,
                'data_type_id' => 20,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 4,
            ),
            128 => 
            array (
                'id' => 132,
                'data_type_id' => 21,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            129 => 
            array (
                'id' => 133,
                'data_type_id' => 21,
                'field' => 'name',
                'type' => 'text',
                'display_name' => 'Name',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 2,
            ),
            130 => 
            array (
                'id' => 134,
                'data_type_id' => 21,
                'field' => 'percentage',
                'type' => 'text',
                'display_name' => 'Value',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"description":"If tax type is \'Fixed\' this value represents a fixed amount, otherwise it is a percentage from the payment amount"}',
                'order' => 3,
            ),
            131 => 
            array (
                'id' => 135,
                'data_type_id' => 21,
                'field' => 'type',
                'type' => 'radio_btn',
                'display_name' => 'Type',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"inclusive","options":{"inclusive":"Inclusive","exclusive":"Exclusive","fixed":"Fixed"}}',
                'order' => 4,
            ),
            132 => 
            array (
                'id' => 136,
                'data_type_id' => 21,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            133 => 
            array (
                'id' => 137,
                'data_type_id' => 21,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 6,
            ),
            134 => 
            array (
                'id' => 138,
                'data_type_id' => 21,
                'field' => 'tax_belongstomany_country_relationship',
                'type' => 'relationship',
                'display_name' => 'Countries',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\Model\\\\Country","table":"countries","type":"belongsToMany","column":"id","key":"id","label":"name","pivot_table":"country_taxes","pivot":"1","taggable":"0"}',
                'order' => 7,
            ),
            135 => 
            array (
                'id' => 142,
                'data_type_id' => 14,
                'field' => 'taxes',
                'type' => 'text',
                'display_name' => 'Taxes',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 24,
            ),
            136 => 
            array (
                'id' => 143,
                'data_type_id' => 1,
                'field' => 'username',
                'type' => 'text',
                'display_name' => 'Username',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 10,
            ),
            137 => 
            array (
                'id' => 145,
                'data_type_id' => 1,
                'field' => 'bio',
                'type' => 'text',
                'display_name' => 'Bio',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 11,
            ),
            138 => 
            array (
                'id' => 146,
                'data_type_id' => 1,
                'field' => 'location',
                'type' => 'text',
                'display_name' => 'Location',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 12,
            ),
            139 => 
            array (
                'id' => 147,
                'data_type_id' => 1,
                'field' => 'website',
                'type' => 'text',
                'display_name' => 'Website',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 13,
            ),
            140 => 
            array (
                'id' => 148,
                'data_type_id' => 1,
                'field' => 'cover',
                'type' => 'image',
                'display_name' => 'Cover',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 7,
            ),
            141 => 
            array (
                'id' => 149,
                'data_type_id' => 1,
                'field' => 'email_verified_at',
                'type' => 'timestamp',
                'display_name' => 'Email verified',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 35,
            ),
            142 => 
            array (
                'id' => 150,
                'data_type_id' => 1,
                'field' => 'public_profile',
                'type' => 'checkbox',
                'display_name' => 'Public Profile',
                'required' => 1,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"On","off":"Off","checked":"true"}',
                'order' => 20,
            ),
            143 => 
            array (
                'id' => 151,
                'data_type_id' => 1,
                'field' => 'profile_access_price',
                'type' => 'text',
                'display_name' => 'Profile Price',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 23,
            ),
            144 => 
            array (
                'id' => 152,
                'data_type_id' => 1,
                'field' => 'billing_address',
                'type' => 'text',
                'display_name' => 'Billing Address',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 27,
            ),
            145 => 
            array (
                'id' => 153,
                'data_type_id' => 1,
                'field' => 'first_name',
                'type' => 'text',
                'display_name' => 'First Name',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 28,
            ),
            146 => 
            array (
                'id' => 154,
                'data_type_id' => 1,
                'field' => 'last_name',
                'type' => 'text',
                'display_name' => 'Last Name',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 29,
            ),
            147 => 
            array (
                'id' => 155,
                'data_type_id' => 1,
                'field' => 'city',
                'type' => 'text',
                'display_name' => 'City',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 30,
            ),
            148 => 
            array (
                'id' => 156,
                'data_type_id' => 1,
                'field' => 'country',
                'type' => 'text',
                'display_name' => 'Country',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 31,
            ),
            149 => 
            array (
                'id' => 157,
                'data_type_id' => 1,
                'field' => 'state',
                'type' => 'text',
                'display_name' => 'State',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 32,
            ),
            150 => 
            array (
                'id' => 158,
                'data_type_id' => 1,
                'field' => 'postcode',
                'type' => 'text',
                'display_name' => 'Postcode',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 33,
            ),
            151 => 
            array (
                'id' => 159,
                'data_type_id' => 27,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 1,
            ),
            152 => 
            array (
                'id' => 160,
                'data_type_id' => 27,
                'field' => 'slug',
                'type' => 'text',
                'display_name' => 'Slug',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 4,
            ),
            153 => 
            array (
                'id' => 161,
                'data_type_id' => 27,
                'field' => 'title',
                'type' => 'text',
                'display_name' => 'Title',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 2,
            ),
            154 => 
            array (
                'id' => 162,
                'data_type_id' => 27,
                'field' => 'content',
                'type' => 'rich_text_box',
                'display_name' => 'Content',
                'required' => 1,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
            'details' => '{"tinymceOptions":{"toolbar":"undo redo | formatselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image | fontsizeselect","plugins":"lists link image","image_title":true,"automatic_uploads":false,"file_picker_types":"image","image_advtab":true,"file_picker_callback":"function(callback, value, meta) { if (meta.filetype === \'image\') { var input = document.createElement(\'input\'); input.setAttribute(\'type\', \'text\'); input.setAttribute(\'placeholder\', \'Enter image URL\'); input.oninput = function() { callback(input.value, {alt: \'\'}); }; document.body.appendChild(input); input.focus(); input.onblur = function() { document.body.removeChild(input); }; } }","fontsize_formats":"8pt 10pt 12pt 14pt 18pt 24pt 36pt","style_formats":[{"title":"Headers","items":[{"title":"Header 1","block":"h1"},{"title":"Header 2","block":"h2"},{"title":"Header 3","block":"h3"},{"title":"Header 4","block":"h4"},{"title":"Header 5","block":"h5"},{"title":"Header 6","block":"h6"}]},{"title":"Inline","items":[{"title":"Bold","icon":"bold","format":"bold"},{"title":"Italic","icon":"italic","format":"italic"},{"title":"Underline","icon":"underline","format":"underline"},{"title":"Strikethrough","icon":"strikethrough","format":"strikethrough"},{"title":"Superscript","icon":"superscript","format":"superscript"},{"title":"Subscript","icon":"subscript","format":"subscript"}]}],"content_style":"body { font-family:Helvetica,Arial,sans-serif; font-size:14px }"}}',
                'order' => 5,
            ),
            155 => 
            array (
                'id' => 163,
                'data_type_id' => 27,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 10,
            ),
            156 => 
            array (
                'id' => 164,
                'data_type_id' => 27,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 11,
            ),
            157 => 
            array (
                'id' => 165,
                'data_type_id' => 14,
                'field' => 'invoice_id',
                'type' => 'text',
                'display_name' => 'InvoiceID',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 13,
            ),
            158 => 
            array (
                'id' => 166,
                'data_type_id' => 28,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            159 => 
            array (
                'id' => 167,
                'data_type_id' => 28,
                'field' => 'user_id',
                'type' => 'text',
                'display_name' => 'User Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            160 => 
            array (
                'id' => 168,
                'data_type_id' => 28,
                'field' => 'files',
                'type' => 'text',
                'display_name' => 'Files',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 1,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 5,
            ),
            161 => 
            array (
                'id' => 169,
                'data_type_id' => 28,
                'field' => 'rejectionReason',
                'type' => 'text',
                'display_name' => 'Rejection Reason',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 7,
            ),
            162 => 
            array (
                'id' => 170,
                'data_type_id' => 28,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 8,
            ),
            163 => 
            array (
                'id' => 171,
                'data_type_id' => 28,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 9,
            ),
            164 => 
            array (
                'id' => 172,
                'data_type_id' => 19,
                'field' => 'message',
                'type' => 'text',
                'display_name' => 'Message',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 8,
            ),
            165 => 
            array (
                'id' => 173,
                'data_type_id' => 28,
                'field' => 'status',
                'type' => 'select_dropdown',
                'display_name' => 'Status',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"pending","options":{"pending":"Pending","rejected":"Rejected","verified":"Verified"}}',
                'order' => 6,
            ),
            166 => 
            array (
                'id' => 179,
                'data_type_id' => 9,
                'field' => 'withdrawal_id',
                'type' => 'text',
                'display_name' => 'Withdrawal Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 13,
            ),
            167 => 
            array (
                'id' => 180,
                'data_type_id' => 9,
                'field' => 'user_message_id',
                'type' => 'text',
                'display_name' => 'User Message Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 15,
            ),
            168 => 
            array (
                'id' => 186,
                'data_type_id' => 17,
                'field' => 'user_list_member_hasone_user_list_relationship',
                'type' => 'relationship',
                'display_name' => 'List name',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 1,
                'details' => '{"model":"App\\\\Model\\\\UserList","table":"user_lists","type":"hasOne","column":"id","key":"list_id","label":"name","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 3,
            ),
            169 => 
            array (
                'id' => 189,
                'data_type_id' => 6,
                'field' => 'message_id',
                'type' => 'text',
                'display_name' => 'Message Id',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 6,
            ),
            170 => 
            array (
                'id' => 192,
                'data_type_id' => 1,
                'field' => 'birthdate',
                'type' => 'date',
                'display_name' => 'Birthdate',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 36,
            ),
            171 => 
            array (
                'id' => 193,
                'data_type_id' => 1,
                'field' => 'identity_verified_at',
                'type' => 'timestamp',
                'display_name' => 'ID verifed',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 37,
            ),
            172 => 
            array (
                'id' => 194,
                'data_type_id' => 29,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            173 => 
            array (
                'id' => 195,
                'data_type_id' => 29,
                'field' => 'from_user_id',
                'type' => 'text',
                'display_name' => 'Reporter ID',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"validation":{"rule":["required"]}}',
                'order' => 3,
            ),
            174 => 
            array (
                'id' => 196,
                'data_type_id' => 29,
                'field' => 'user_id',
                'type' => 'text',
                'display_name' => 'Reported ID',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"validation":{"rule":["required"]}}',
                'order' => 5,
            ),
            175 => 
            array (
                'id' => 197,
                'data_type_id' => 29,
                'field' => 'post_id',
                'type' => 'text',
                'display_name' => 'PostID',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 6,
            ),
            176 => 
            array (
                'id' => 198,
                'data_type_id' => 29,
                'field' => 'details',
                'type' => 'text',
                'display_name' => 'Details',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 9,
            ),
            177 => 
            array (
                'id' => 199,
                'data_type_id' => 29,
                'field' => 'type',
                'type' => 'select_dropdown',
                'display_name' => 'Type',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
            'details' => '{"default":"I don\'t like this post","options":{"I don\'t like this post":"I don\'t like this post","Content is offensive or violates Terms of Service.":"Content is offensive or violates Terms of Service.","Content contains stolen material (DMCA)":"Content contains stolen material (DMCA)","Content is spam":"Content is spam","Report abuse":"Report abuse"}}',
                'order' => 10,
            ),
            178 => 
            array (
                'id' => 200,
                'data_type_id' => 29,
                'field' => 'status',
                'type' => 'select_dropdown',
                'display_name' => 'Status',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"received","options":{"received":"Received","seen":"Seen","solved":"Solved"}}',
                'order' => 11,
            ),
            179 => 
            array (
                'id' => 201,
                'data_type_id' => 29,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 12,
            ),
            180 => 
            array (
                'id' => 202,
                'data_type_id' => 29,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 13,
            ),
            181 => 
            array (
                'id' => 205,
                'data_type_id' => 30,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            182 => 
            array (
                'id' => 206,
                'data_type_id' => 30,
                'field' => 'email',
                'type' => 'text',
                'display_name' => 'Email',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 2,
            ),
            183 => 
            array (
                'id' => 207,
                'data_type_id' => 30,
                'field' => 'subject',
                'type' => 'text',
                'display_name' => 'Subject',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            184 => 
            array (
                'id' => 208,
                'data_type_id' => 30,
                'field' => 'message',
                'type' => 'text',
                'display_name' => 'Message',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 4,
            ),
            185 => 
            array (
                'id' => 209,
                'data_type_id' => 30,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            186 => 
            array (
                'id' => 210,
                'data_type_id' => 30,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 6,
            ),
            187 => 
            array (
                'id' => 223,
                'data_type_id' => 32,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            188 => 
            array (
                'id' => 224,
                'data_type_id' => 32,
                'field' => 'user_id',
                'type' => 'text',
                'display_name' => 'User Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 2,
            ),
            189 => 
            array (
                'id' => 225,
                'data_type_id' => 32,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 4,
            ),
            190 => 
            array (
                'id' => 226,
                'data_type_id' => 32,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 5,
            ),
            191 => 
            array (
                'id' => 227,
                'data_type_id' => 32,
                'field' => 'featured_user_hasone_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Username',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 3,
            ),
            192 => 
            array (
                'id' => 228,
                'data_type_id' => 33,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            193 => 
            array (
                'id' => 229,
                'data_type_id' => 33,
                'field' => 'user_id',
                'type' => 'text',
                'display_name' => 'User Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            194 => 
            array (
                'id' => 230,
                'data_type_id' => 33,
                'field' => 'transaction_id',
                'type' => 'text',
                'display_name' => 'Transaction Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 4,
            ),
            195 => 
            array (
                'id' => 231,
                'data_type_id' => 33,
                'field' => 'status',
                'type' => 'select_dropdown',
                'display_name' => 'Status',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"pending","options":{"pending":"Pending","rejected":"Rejected","approved":"Approved"}}',
                'order' => 5,
            ),
            196 => 
            array (
                'id' => 232,
                'data_type_id' => 33,
                'field' => 'type',
                'type' => 'select_dropdown',
                'display_name' => 'Type',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"deposit","options":{"deposit":"Deposit"}}',
                'order' => 6,
            ),
            197 => 
            array (
                'id' => 233,
                'data_type_id' => 33,
                'field' => 'reason',
                'type' => 'text',
                'display_name' => 'Reason',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 7,
            ),
            198 => 
            array (
                'id' => 234,
                'data_type_id' => 33,
                'field' => 'message',
                'type' => 'text',
                'display_name' => 'Message',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 8,
            ),
            199 => 
            array (
                'id' => 235,
                'data_type_id' => 33,
                'field' => 'amount',
                'type' => 'text',
                'display_name' => 'Amount',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 9,
            ),
            200 => 
            array (
                'id' => 236,
                'data_type_id' => 33,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 10,
            ),
            201 => 
            array (
                'id' => 237,
                'data_type_id' => 33,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 11,
            ),
            202 => 
            array (
                'id' => 238,
                'data_type_id' => 33,
                'field' => 'payment_request_hasmany_attachment_relationship',
                'type' => 'relationship',
                'display_name' => 'Files',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 0,
                'details' => '{"model":"App\\\\Model\\\\Attachment","table":"attachments","type":"hasMany","column":"payment_request_id","key":"id","label":"filename","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 12,
            ),
            203 => 
            array (
                'id' => 239,
                'data_type_id' => 11,
                'field' => 'price',
                'type' => 'text',
                'display_name' => 'Price',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 4,
            ),
            204 => 
            array (
                'id' => 240,
                'data_type_id' => 19,
                'field' => 'payment_method',
                'type' => 'text',
                'display_name' => 'Payment Method',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 10,
            ),
            205 => 
            array (
                'id' => 241,
                'data_type_id' => 19,
                'field' => 'payment_identifier',
                'type' => 'text',
                'display_name' => 'Payment Identifier',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 11,
            ),
            206 => 
            array (
                'id' => 242,
                'data_type_id' => 34,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 1,
            ),
            207 => 
            array (
                'id' => 243,
                'data_type_id' => 34,
                'field' => 'invoice_id',
                'type' => 'text',
                'display_name' => 'Invoice Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 2,
            ),
            208 => 
            array (
                'id' => 244,
                'data_type_id' => 34,
                'field' => 'data',
                'type' => 'code_editor',
                'display_name' => 'Data',
                'required' => 1,
                'browse' => 0,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 4,
            ),
            209 => 
            array (
                'id' => 245,
                'data_type_id' => 34,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            210 => 
            array (
                'id' => 246,
                'data_type_id' => 34,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 6,
            ),
            211 => 
            array (
                'id' => 247,
                'data_type_id' => 13,
                'field' => 'ccbill_subscription_id',
                'type' => 'text',
                'display_name' => 'Ccbill Id',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 12,
            ),
            212 => 
            array (
                'id' => 248,
                'data_type_id' => 13,
                'field' => 'provider',
                'type' => 'select_dropdown',
                'display_name' => 'Provider',
                'required' => 1,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"credit","options":{"stripe":"Stripe","paypal":"Paypal","ccbill":"CCBill","credit":"Credit"}}',
                'order' => 8,
            ),
            213 => 
            array (
                'id' => 249,
                'data_type_id' => 1,
                'field' => 'gender_id',
                'type' => 'text',
                'display_name' => 'Gender Id',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 15,
            ),
            214 => 
            array (
                'id' => 250,
                'data_type_id' => 1,
                'field' => 'gender_pronoun',
                'type' => 'text',
                'display_name' => 'Gender Pronoun',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 16,
            ),
            215 => 
            array (
                'id' => 251,
                'data_type_id' => 1,
                'field' => 'paid_profile',
                'type' => 'checkbox',
                'display_name' => 'Paid Profile',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"On","off":"Off","checked":"true"}',
                'order' => 22,
            ),
            216 => 
            array (
                'id' => 252,
                'data_type_id' => 1,
                'field' => 'profile_access_price_6_months',
                'type' => 'text',
                'display_name' => 'Profile for Price 6 Months',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 25,
            ),
            217 => 
            array (
                'id' => 253,
                'data_type_id' => 1,
                'field' => 'profile_access_price_3_months',
                'type' => 'text',
                'display_name' => 'Profile for Price 3 Months',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 24,
            ),
            218 => 
            array (
                'id' => 254,
                'data_type_id' => 1,
                'field' => 'profile_access_price_12_months',
                'type' => 'text',
                'display_name' => 'Profile for Price 12 Months',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 26,
            ),
            219 => 
            array (
                'id' => 255,
                'data_type_id' => 1,
                'field' => 'auth_provider',
                'type' => 'text',
                'display_name' => 'Auth Provider',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 39,
            ),
            220 => 
            array (
                'id' => 256,
                'data_type_id' => 1,
                'field' => 'auth_provider_id',
                'type' => 'text',
                'display_name' => 'Auth Provider Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 40,
            ),
            221 => 
            array (
                'id' => 257,
                'data_type_id' => 1,
                'field' => 'enable_2fa',
                'type' => 'checkbox',
                'display_name' => 'Enable 2fa',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{"on":"On","off":"Off","checked":"false"}',
                'order' => 41,
            ),
            222 => 
            array (
                'id' => 258,
                'data_type_id' => 1,
                'field' => 'enable_geoblocking',
                'type' => 'checkbox',
                'display_name' => 'Enable Geoblocking',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{"on":"On","off":"Off","checked":"false"}',
                'order' => 42,
            ),
            223 => 
            array (
                'id' => 259,
                'data_type_id' => 1,
                'field' => 'open_profile',
                'type' => 'checkbox',
                'display_name' => 'Open Profile',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"On","off":"Off","checked":"false"}',
                'order' => 21,
            ),
            224 => 
            array (
                'id' => 263,
                'data_type_id' => 27,
                'field' => 'page_order',
                'type' => 'text',
                'display_name' => 'Order',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 6,
            ),
            225 => 
            array (
                'id' => 264,
                'data_type_id' => 27,
                'field' => 'shown_in_footer',
                'type' => 'checkbox',
                'display_name' => 'Shown in Footer',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"On","off":"Off","checked":"true"}',
                'order' => 7,
            ),
            226 => 
            array (
                'id' => 265,
                'data_type_id' => 19,
                'field' => 'fee',
                'type' => 'text',
                'display_name' => 'Fee',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            227 => 
            array (
                'id' => 266,
                'data_type_id' => 19,
                'field' => 'processed',
                'type' => 'checkbox',
                'display_name' => 'Processed',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"True","off":"False","checked":"false"}',
                'order' => 7,
            ),
            228 => 
            array (
                'id' => 267,
                'data_type_id' => 14,
                'field' => 'stream_id',
                'type' => 'text',
                'display_name' => 'StreamID',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 14,
            ),
            229 => 
            array (
                'id' => 268,
                'data_type_id' => 14,
                'field' => 'user_message_id',
                'type' => 'text',
                'display_name' => 'MessageID',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 17,
            ),
            230 => 
            array (
                'id' => 269,
                'data_type_id' => 14,
                'field' => 'coinbase_charge_id',
                'type' => 'text',
                'display_name' => 'Coinbase Charge Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 20,
            ),
            231 => 
            array (
                'id' => 270,
                'data_type_id' => 14,
                'field' => 'coinbase_transaction_token',
                'type' => 'text',
                'display_name' => 'Coinbase Transaction Token',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 22,
            ),
            232 => 
            array (
                'id' => 271,
                'data_type_id' => 14,
                'field' => 'nowpayments_payment_id',
                'type' => 'text',
                'display_name' => 'Nowpayments Payment Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 23,
            ),
            233 => 
            array (
                'id' => 272,
                'data_type_id' => 14,
                'field' => 'nowpayments_order_id',
                'type' => 'text',
                'display_name' => 'Nowpayments Order Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 27,
            ),
            234 => 
            array (
                'id' => 273,
                'data_type_id' => 14,
                'field' => 'ccbill_payment_token',
                'type' => 'text',
                'display_name' => 'Ccbill Payment Token',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 29,
            ),
            235 => 
            array (
                'id' => 274,
                'data_type_id' => 14,
                'field' => 'ccbill_transaction_id',
                'type' => 'text',
                'display_name' => 'Ccbill Transaction Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 31,
            ),
            236 => 
            array (
                'id' => 275,
                'data_type_id' => 14,
                'field' => 'ccbill_subscription_id',
                'type' => 'text',
                'display_name' => 'Ccbill Subscription Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 32,
            ),
            237 => 
            array (
                'id' => 276,
                'data_type_id' => 14,
                'field' => 'paystack_payment_token',
                'type' => 'text',
                'display_name' => 'Paystack Payment Token',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 33,
            ),
            238 => 
            array (
                'id' => 278,
                'data_type_id' => 1,
                'field' => 'referral_code',
                'type' => 'text',
                'display_name' => 'Referral Code',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 9,
            ),
            239 => 
            array (
                'id' => 298,
                'data_type_id' => 37,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            240 => 
            array (
                'id' => 299,
                'data_type_id' => 37,
                'field' => 'user_id',
                'type' => 'text',
                'display_name' => 'User Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            241 => 
            array (
                'id' => 300,
                'data_type_id' => 37,
                'field' => 'stream_id',
                'type' => 'text',
                'display_name' => 'Stream Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 4,
            ),
            242 => 
            array (
                'id' => 301,
                'data_type_id' => 37,
                'field' => 'message',
                'type' => 'text',
                'display_name' => 'Message',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            243 => 
            array (
                'id' => 302,
                'data_type_id' => 37,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 6,
            ),
            244 => 
            array (
                'id' => 303,
                'data_type_id' => 37,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 7,
            ),
            245 => 
            array (
                'id' => 304,
                'data_type_id' => 38,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 1,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            246 => 
            array (
                'id' => 305,
                'data_type_id' => 38,
                'field' => 'user_id',
                'type' => 'text',
                'display_name' => 'User Id',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            247 => 
            array (
                'id' => 306,
                'data_type_id' => 38,
                'field' => 'status',
                'type' => 'select_dropdown',
                'display_name' => 'Status',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"requested","options":{"in-progress":"In progress","ended":"Ended","deleted":"Deleted"}}',
                'order' => 4,
            ),
            248 => 
            array (
                'id' => 307,
                'data_type_id' => 38,
                'field' => 'name',
                'type' => 'text',
                'display_name' => 'Name',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            249 => 
            array (
                'id' => 308,
                'data_type_id' => 38,
                'field' => 'slug',
                'type' => 'text',
                'display_name' => 'Slug',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 6,
            ),
            250 => 
            array (
                'id' => 309,
                'data_type_id' => 38,
                'field' => 'poster',
                'type' => 'text',
                'display_name' => 'Poster',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 11,
            ),
            251 => 
            array (
                'id' => 310,
                'data_type_id' => 38,
                'field' => 'price',
                'type' => 'text',
                'display_name' => 'Price',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 7,
            ),
            252 => 
            array (
                'id' => 311,
                'data_type_id' => 38,
                'field' => 'requires_subscription',
                'type' => 'checkbox',
                'display_name' => 'Requires Subscription',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"True","off":"False","checked":"true"}',
                'order' => 8,
            ),
            253 => 
            array (
                'id' => 312,
                'data_type_id' => 38,
                'field' => 'sent_expiring_reminder',
                'type' => 'checkbox',
                'display_name' => 'Sent Expiring Reminder',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"True","off":"False","checked":"true"}',
                'order' => 9,
            ),
            254 => 
            array (
                'id' => 313,
                'data_type_id' => 38,
                'field' => 'is_public',
                'type' => 'checkbox',
                'display_name' => 'Is Public',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"True","off":"False","checked":"true"}',
                'order' => 10,
            ),
            255 => 
            array (
                'id' => 314,
                'data_type_id' => 38,
                'field' => 'pushr_id',
                'type' => 'text',
                'display_name' => 'Pushr Id',
                'required' => 1,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 12,
            ),
            256 => 
            array (
                'id' => 315,
                'data_type_id' => 38,
                'field' => 'rtmp_key',
                'type' => 'text',
                'display_name' => 'Rtmp Key',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 13,
            ),
            257 => 
            array (
                'id' => 316,
                'data_type_id' => 38,
                'field' => 'rtmp_server',
                'type' => 'text',
                'display_name' => 'Rtmp Server',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 14,
            ),
            258 => 
            array (
                'id' => 317,
                'data_type_id' => 38,
                'field' => 'hls_link',
                'type' => 'text',
                'display_name' => 'Hls Link',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 15,
            ),
            259 => 
            array (
                'id' => 318,
                'data_type_id' => 38,
                'field' => 'vod_link',
                'type' => 'text',
                'display_name' => 'Vod Link',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 16,
            ),
            260 => 
            array (
                'id' => 319,
                'data_type_id' => 38,
                'field' => 'settings',
                'type' => 'text',
                'display_name' => 'Settings',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 17,
            ),
            261 => 
            array (
                'id' => 320,
                'data_type_id' => 38,
                'field' => 'ended_at',
                'type' => 'text',
                'display_name' => 'Ended At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 18,
            ),
            262 => 
            array (
                'id' => 321,
                'data_type_id' => 38,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 19,
            ),
            263 => 
            array (
                'id' => 322,
                'data_type_id' => 38,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 20,
            ),
            264 => 
            array (
                'id' => 323,
                'data_type_id' => 39,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            265 => 
            array (
                'id' => 324,
                'data_type_id' => 39,
                'field' => 'used_by',
                'type' => 'text',
            'display_name' => 'Used By (UserID)',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            266 => 
            array (
                'id' => 325,
                'data_type_id' => 39,
                'field' => 'referral_code',
                'type' => 'text',
                'display_name' => 'Referral Code',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 2,
            ),
            267 => 
            array (
                'id' => 326,
                'data_type_id' => 39,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 4,
            ),
            268 => 
            array (
                'id' => 327,
                'data_type_id' => 39,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 5,
            ),
            269 => 
            array (
                'id' => 328,
                'data_type_id' => 40,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            270 => 
            array (
                'id' => 329,
                'data_type_id' => 40,
                'field' => 'from_user_id',
                'type' => 'text',
                'display_name' => 'From User Id',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 3,
            ),
            271 => 
            array (
                'id' => 330,
                'data_type_id' => 40,
                'field' => 'to_user_id',
                'type' => 'text',
                'display_name' => 'To User Id',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 5,
            ),
            272 => 
            array (
                'id' => 331,
                'data_type_id' => 40,
                'field' => 'transaction_id',
                'type' => 'text',
                'display_name' => 'Transaction Id',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 6,
            ),
            273 => 
            array (
                'id' => 332,
                'data_type_id' => 40,
                'field' => 'referral_code_usage_id',
                'type' => 'text',
                'display_name' => 'Referral Code Usage Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 7,
            ),
            274 => 
            array (
                'id' => 333,
                'data_type_id' => 40,
                'field' => 'amount',
                'type' => 'text',
                'display_name' => 'Amount',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 8,
            ),
            275 => 
            array (
                'id' => 334,
                'data_type_id' => 40,
                'field' => 'reward_type',
                'type' => 'text',
                'display_name' => 'Reward Type',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 9,
            ),
            276 => 
            array (
                'id' => 335,
                'data_type_id' => 40,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 10,
            ),
            277 => 
            array (
                'id' => 336,
                'data_type_id' => 40,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 11,
            ),
            278 => 
            array (
                'id' => 337,
                'data_type_id' => 21,
                'field' => 'hidden',
                'type' => 'checkbox',
                'display_name' => 'Hidden',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 7,
            ),
            279 => 
            array (
                'id' => 338,
                'data_type_id' => 27,
                'field' => 'is_tos',
                'type' => 'checkbox',
                'display_name' => 'Is Tos',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"On","off":"Off","checked":"true"}',
                'order' => 8,
            ),
            280 => 
            array (
                'id' => 339,
                'data_type_id' => 27,
                'field' => 'is_privacy',
                'type' => 'checkbox',
                'display_name' => 'Is Privacy',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"On","off":"Off","checked":"true"}',
                'order' => 9,
            ),
            281 => 
            array (
                'id' => 340,
                'data_type_id' => 27,
                'field' => 'short_title',
                'type' => 'text',
                'display_name' => 'Short Title',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"description":"If provided, it will be used in places where a shorter version of the page name is needed."}',
                'order' => 3,
            ),
            282 => 
            array (
                'id' => 341,
                'data_type_id' => 1,
                'field' => 'user_belongsto_user_gender_relationship',
                'type' => 'relationship',
                'display_name' => 'Gender',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{"model":"App\\\\Model\\\\UserGender","table":"user_genders","type":"belongsTo","column":"gender_id","key":"id","label":"gender_name","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 14,
            ),
            283 => 
            array (
                'id' => 342,
                'data_type_id' => 4,
                'field' => 'wallet_hasone_user_relationship_1',
                'type' => 'relationship',
                'display_name' => 'Name',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"hasOne","column":"id","key":"user_id","label":"name","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 4,
            ),
            284 => 
            array (
                'id' => 343,
                'data_type_id' => 28,
                'field' => 'user_verify_hasone_user_relationship_1',
                'type' => 'relationship',
                'display_name' => 'Name',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"hasOne","column":"id","key":"user_id","label":"name","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 4,
            ),
            285 => 
            array (
                'id' => 344,
                'data_type_id' => 11,
                'field' => 'release_date',
                'type' => 'timestamp',
                'display_name' => 'Release Date',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 8,
            ),
            286 => 
            array (
                'id' => 345,
                'data_type_id' => 11,
                'field' => 'expire_date',
                'type' => 'timestamp',
                'display_name' => 'Expire Date',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 9,
            ),
            287 => 
            array (
                'id' => 346,
                'data_type_id' => 11,
                'field' => 'is_pinned',
                'type' => 'checkbox',
                'display_name' => 'Is Pinned',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"On","off":"Off","checked":"true"}',
                'order' => 7,
            ),
            288 => 
            array (
                'id' => 348,
                'data_type_id' => 6,
                'field' => 'coconut_id',
                'type' => 'text',
                'display_name' => 'Coconut Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 10,
            ),
            289 => 
            array (
                'id' => 349,
                'data_type_id' => 6,
                'field' => 'has_thumbnail',
                'type' => 'text',
                'display_name' => 'Has Thumbnail',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 12,
            ),
            290 => 
            array (
                'id' => 350,
                'data_type_id' => 6,
                'field' => 'payment_request_id',
                'type' => 'text',
                'display_name' => 'Payment Request Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 0,
                'details' => '{}',
                'order' => 5,
            ),
            291 => 
            array (
                'id' => 353,
                'data_type_id' => 29,
                'field' => 'user_report_hasone_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Reporter',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"from_user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            292 => 
            array (
                'id' => 354,
                'data_type_id' => 29,
                'field' => 'user_report_hasone_user_relationship_1',
                'type' => 'relationship',
                'display_name' => 'Reported',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 4,
            ),
            293 => 
            array (
                'id' => 355,
                'data_type_id' => 9,
                'field' => 'stream_id',
                'type' => 'text',
                'display_name' => 'Stream Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 12,
            ),
            294 => 
            array (
                'id' => 356,
                'data_type_id' => 9,
                'field' => 'read',
                'type' => 'checkbox',
                'display_name' => 'Read',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"True","off":"False","checked":"true"}',
                'order' => 14,
            ),
            295 => 
            array (
                'id' => 357,
                'data_type_id' => 18,
                'field' => 'price',
                'type' => 'text',
                'display_name' => 'Price',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 7,
            ),
            296 => 
            array (
                'id' => 358,
                'data_type_id' => 28,
                'field' => 'user_verify_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Username',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            297 => 
            array (
                'id' => 359,
                'data_type_id' => 4,
                'field' => 'wallet_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Username',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            298 => 
            array (
                'id' => 360,
                'data_type_id' => 9,
                'field' => 'notification_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'From',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"from_user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            299 => 
            array (
                'id' => 361,
                'data_type_id' => 9,
                'field' => 'notification_belongsto_user_relationship_1',
                'type' => 'relationship',
                'display_name' => 'To',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"to_user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 4,
            ),
            300 => 
            array (
                'id' => 362,
                'data_type_id' => 18,
                'field' => 'user_message_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Sender',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"sender_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            301 => 
            array (
                'id' => 363,
                'data_type_id' => 18,
                'field' => 'user_message_belongsto_user_relationship_1',
                'type' => 'relationship',
                'display_name' => 'Receiver',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"receiver_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 4,
            ),
            302 => 
            array (
                'id' => 364,
                'data_type_id' => 12,
                'field' => 'reaction_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Username',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            303 => 
            array (
                'id' => 365,
                'data_type_id' => 16,
                'field' => 'user_list_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Username',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            304 => 
            array (
                'id' => 366,
                'data_type_id' => 17,
                'field' => 'user_list_member_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Member',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 4,
            ),
            305 => 
            array (
                'id' => 368,
                'data_type_id' => 11,
                'field' => 'post_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Username',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            306 => 
            array (
                'id' => 369,
                'data_type_id' => 6,
                'field' => 'attachment_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Username',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            307 => 
            array (
                'id' => 370,
                'data_type_id' => 10,
                'field' => 'post_comment_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Username',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            308 => 
            array (
                'id' => 371,
                'data_type_id' => 15,
                'field' => 'user_bookmark_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Username',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            309 => 
            array (
                'id' => 372,
                'data_type_id' => 38,
                'field' => 'stream_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Username',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            310 => 
            array (
                'id' => 373,
                'data_type_id' => 37,
                'field' => 'stream_message_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Username',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            311 => 
            array (
                'id' => 374,
                'data_type_id' => 13,
                'field' => 'subscription_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Subscriber',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"sender_user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            312 => 
            array (
                'id' => 375,
                'data_type_id' => 13,
                'field' => 'subscription_belongsto_user_relationship_1',
                'type' => 'relationship',
                'display_name' => 'Creator',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"recipient_user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 4,
            ),
            313 => 
            array (
                'id' => 376,
                'data_type_id' => 14,
                'field' => 'transaction_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Sender',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"sender_user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            314 => 
            array (
                'id' => 377,
                'data_type_id' => 14,
                'field' => 'transaction_belongsto_user_relationship_1',
                'type' => 'relationship',
                'display_name' => 'Recipient',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"recipient_user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 4,
            ),
            315 => 
            array (
                'id' => 378,
                'data_type_id' => 14,
                'field' => 'mercado_payment_token',
                'type' => 'text',
                'display_name' => 'Mercado Payment Token',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 25,
            ),
            316 => 
            array (
                'id' => 379,
                'data_type_id' => 14,
                'field' => 'mercado_payment_id',
                'type' => 'text',
                'display_name' => 'Mercado Payment Id',
                'required' => 0,
                'browse' => 0,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 26,
            ),
            317 => 
            array (
                'id' => 380,
                'data_type_id' => 19,
                'field' => 'withdrawal_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Username',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            318 => 
            array (
                'id' => 381,
                'data_type_id' => 33,
                'field' => 'payment_request_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Username',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            319 => 
            array (
                'id' => 382,
                'data_type_id' => 34,
                'field' => 'invoice_hasone_transaction_relationship',
                'type' => 'relationship',
                'display_name' => 'Transaction Id',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 0,
                'add' => 0,
                'delete' => 1,
                'details' => '{"model":"App\\\\Model\\\\Transaction","table":"transactions","type":"belongsTo","column":"invoice_id","key":"id","label":"id","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 3,
            ),
            320 => 
            array (
                'id' => 383,
                'data_type_id' => 40,
                'field' => 'reward_belongsto_user_relationship',
                'type' => 'relationship',
                'display_name' => 'Referral',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"from_user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 2,
            ),
            321 => 
            array (
                'id' => 384,
                'data_type_id' => 40,
                'field' => 'reward_belongsto_user_relationship_1',
                'type' => 'relationship',
                'display_name' => 'Referred',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"model":"App\\\\User","table":"users","type":"belongsTo","column":"to_user_id","key":"id","label":"username","pivot_table":"attachments","pivot":"0","taggable":"0"}',
                'order' => 4,
            ),
            322 => 
            array (
                'id' => 385,
                'data_type_id' => 29,
                'field' => 'message_id',
                'type' => 'text',
                'display_name' => 'MessageID',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 7,
            ),
            323 => 
            array (
                'id' => 386,
                'data_type_id' => 29,
                'field' => 'stream_id',
                'type' => 'text',
                'display_name' => 'StreamID',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 8,
            ),
            324 => 
            array (
                'id' => 387,
                'data_type_id' => 42,
                'field' => 'id',
                'type' => 'text',
                'display_name' => 'Id',
                'required' => 1,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 1,
            ),
            325 => 
            array (
                'id' => 388,
                'data_type_id' => 42,
                'field' => 'content',
                'type' => 'rich_text_box',
                'display_name' => 'Content',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"description":"The .global-announcement-banner .content{} class can be used for additional styling."}',
                'order' => 2,
            ),
            326 => 
            array (
                'id' => 389,
                'data_type_id' => 42,
                'field' => 'is_published',
                'type' => 'checkbox',
                'display_name' => 'Is Published',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"True","off":"False","checked":"true","description":"If disabled, only admins will be able to see the banner"}',
                'order' => 3,
            ),
            327 => 
            array (
                'id' => 390,
                'data_type_id' => 42,
                'field' => 'is_dismissible',
                'type' => 'checkbox',
                'display_name' => 'Is Dismissible',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"True","off":"False","checked":"true"}',
                'order' => 4,
            ),
            328 => 
            array (
                'id' => 391,
                'data_type_id' => 42,
                'field' => 'is_global',
                'type' => 'checkbox',
                'display_name' => 'Is Global',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"True","off":"False","checked":"false","description":"If enabled, the banner will be shown on all pages, if not, it\'s only displayed on the homepage."}',
                'order' => 6,
            ),
            329 => 
            array (
                'id' => 392,
                'data_type_id' => 42,
                'field' => 'created_at',
                'type' => 'timestamp',
                'display_name' => 'Created At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 0,
                'delete' => 1,
                'details' => '{}',
                'order' => 9,
            ),
            330 => 
            array (
                'id' => 393,
                'data_type_id' => 42,
                'field' => 'updated_at',
                'type' => 'timestamp',
                'display_name' => 'Updated At',
                'required' => 0,
                'browse' => 0,
                'read' => 0,
                'edit' => 0,
                'add' => 0,
                'delete' => 0,
                'details' => '{}',
                'order' => 10,
            ),
            331 => 
            array (
                'id' => 395,
                'data_type_id' => 42,
                'field' => 'is_sticky',
                'type' => 'checkbox',
                'display_name' => 'Is Sticky',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"on":"True","off":"False","checked":"true"}',
                'order' => 5,
            ),
            332 => 
            array (
                'id' => 396,
                'data_type_id' => 42,
                'field' => 'expiring_at',
                'type' => 'timestamp',
                'display_name' => 'Expiring At',
                'required' => 0,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{}',
                'order' => 8,
            ),
            333 => 
            array (
                'id' => 397,
                'data_type_id' => 42,
                'field' => 'size',
                'type' => 'select_dropdown',
                'display_name' => 'Size',
                'required' => 1,
                'browse' => 1,
                'read' => 1,
                'edit' => 1,
                'add' => 1,
                'delete' => 1,
                'details' => '{"default":"regular","options":{"small":"Small","regular":"Regular"}}',
                'order' => 7,
            ),
        ));
        
        
    }
}