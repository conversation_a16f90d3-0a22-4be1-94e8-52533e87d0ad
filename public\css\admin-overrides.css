.page-content.settings .tab-pane{
    padding: 1rem 1.5rem;
}

.app-container .side-menu .panel.widget h4 {
    font-size: 16px!important;
    margin-top: 20px!important;
    margin-left: 5px!important;
}

.identity-files-preview{
    padding-left:15px;
    margin-bottom: 10px;
}

.identity-files-preview img{
    width: 150px;
    height: 150px;
}


.payments-info, .social-login-info, .invoices-info, .emails-info{
    /*display: none;*/
}

.tab-additional-info{
    /*display: none;*/
    padding-bottom: 0px!important;
}

.tab-additional-info .alert{
    margin-bottom: 0px;
}

.tab-additional-info .info-label{
    margin-bottom: 4px;
}

.tab-additional-info .icon{
    display: inline-block;
    margin-right:5px;
}

.tab-additional-info ul{
    margin-left:20px;
}

.tab-additional-info li{
   margin-top:4px;
}

.admin-id-asset{
    width:200px;
    height:200px
}

.metrics-container{
    font-size: 16px!important;
}

.metrics-container .row {
    margin-bottom: 0px!important;
}


.settings .panel-body{
    padding-left: 0px;
}

.settings #payments{
    /*padding-top:0px;*/
}

/* TODO: Re-use some of these (Settings & Compass menus overrides) */
.compass .voyager .nav-tabs>li.active>a:hover {
    background-color: #CB0C9F!important;
}

.compass .nav-tabs>li.active>a, .nav-tabs>li.active>a:focus, .nav-tabs>li.active>a:hover {
    color: #fff;
    background-color: #CB0C9F;
    border-color: transparent transparent #CB0C9F;
}

.compass .nav-tabs>li.active>a:hover {
    background-color: #CB0C9F;
}

.compass .panel>:not(.panel-loading):not(.collapsing) {
    padding-left: 0px!important;
}

.settings .nav-tabs li{
    font-weight: 500!important;
}

.settings .panel-title {
    font-size: 16px;
}

.settings .voyager .nav-tabs>li.active>a:hover {
    background-color: #CB0C9F!important;
}

.settings .nav-tabs>li.active>a, .nav-tabs>li.active>a:focus, .nav-tabs>li.active>a:hover {
    color: #fff!important;
    background-color: #CB0C9F!important;
    border-color: transparent transparent #CB0C9F!important;
}

.settings .nav-tabs>li.active>a:hover {
    background-color: #CB0C9F!important;
}

.settings .panel>:not(.panel-loading):not(.collapsing) {
    padding-left: 0px!important;
}

.voyager.users #dataTable img{
    width: 48px!important;
    height: 48px!important;
    border-radius: 100%!important;
}

.voyager.users .actions{
    width:230px;
}

.settings .ace_editor.min_height_400 {
    min-height: 200px!important;
    width: 100%;
}

.primary-link{
    color: #CB0C9F!important;
}

.primary-link:hover{
    color: #E585CF!important;
}

.info-category-bg{
    background: #ffc4f1;width:64px;height:64px;border-radius: 40%
}

.info-category-icon{
    font-size: 44px;color:#f648ce;padding-top:10px
}

.dashboard a{
    text-decoration: none!important;
}

/**
* Bootstrap 4 polyfills
 */

.d-flex{
    display: flex!important;
}

.d-none{
    display: none!important;
}

.mb-4{
    margin-bottom: 1.5rem!important;
}

.mb-1{
    margin-bottom: 1rem!important;
}

.mb-2{
    margin-bottom: 1.2rem!important;
}

.mt-2{
    margin-top: 1.2rem!important;
}

.p-5{
    padding:1.6rem;
}

.mt-05{
    margin-top:0.6rem;
}

.mt-3{
    margin-top:1.3rem;
}

.mt-10{
    margin-top:10px;
}

.m-0{
    margin:0!important;
}

.ml-half-1{
    margin-left: .5rem!important;
}

.ml-2{
    margin-left:1.2rem;
}

.ml-4{
    margin-left:1.4rem;
}

.justify-content-center {
    justify-content: center!important;
}

.align-items-center {
    align-items: center!important;
}

.font-weight-bolder{
    font-weight: 600!important;
}

.rounded{
    border-radius: 0.35rem!important;
}

.h-100{
    height: 100%!important;
}


.two-columns-graph-holder{
    display: flex!important;
}

.text-success{
    color:#2ECC71!important;
}

.text-danger{
    color:#ea0606!important;
}

@media (max-width: 767px) {
    .two-columns-graph-holder{
        display: block!important;
    }
}

.icon.voyager-angle-down, .icon.voyager-angle-up{
    margin-top:3px;
    margin-right: 3px;
}

.edit-add-bread {
    width:0;
    height:0;
    overflow:hidden;
}

.setting-value-image {
    width:200px;
    height:auto;
    padding:2px;
    border:1px solid #ddd;
    margin-bottom:10px;
}

.settings-upload {
    width:0;
    height:0;
    overflow:hidden;
}

.bgimage{
    background-size: cover;
    background-position: 0px;
}


.bread-label{
    padding: 5px;
}

.user-edit-avatar{
    width:200px;
    height:auto;
    clear:both;
    display:block;
    padding:2px;
    border:1px solid #ddd;
    margin-bottom:10px;
}

.user-save-form{
    width:0px;
    height:0;
    overflow:hidden
}

.alert .close {
    background: transparent;
}

.w-50p{
    width: 50px;
}

.w-100p{
    width: 100px;
}

.docs-text {
    color: #FFFFFF!important;
    text-decoration: none!important;
}

.nav-tabs>li>a {
    padding-top: 10px;
    padding-right: 12px;
    padding-bottom: 10px;
    padding-left: 12px;
    color: #76838f;
    transition: .25s;
}

.pickr .pcr-button::before {
    background: none!important;
}

.pcr-color-preview{
    display: none!important;
}

.pcr-app[data-theme='nano'] .pcr-swatches {
    margin-top: 0.1em!important;
    padding: 0 0.9em!important;
}

.pcr-app[data-theme='nano'] .pcr-selection {
    grid-gap: 0.3em!important;
}
.mb-0{
    margin-bottom: 0px!important;
}

.tab-additional-info a{
    color:#FFF;
    font-weight: 500;
}

.tab-additional-info a:hover{
    color:#f1f1f1;
}

/**
Additional themes overrides
 */

/*.voyager .side-menu.sidebar-inverse {*/
/*    background: #293042!important;*/
/*}*/

/*.voyager .side-menu.sidebar-inverse .navbar li>a:hover {*/
/*    color: #fff;*/
/*    background: #2d364c!important;*/
/*}*/


/*#voyager-loader {*/
/*   background-color: #EDEEF0!important;*/
/*}*/

/*.app-container {*/
/*    background-color: #EDEEF0!important;*/
/*}*/

/*body{*/
/*    background-color: #EDEEF0!important;*/
/*}*/

/*    background-color: #EDEEF0; */


.table {
    margin-bottom: 0px;
}

.voyager .table>thead>tr>th {
    padding-top: 15px;
    padding-bottom: 15px;
}


th{
    color: #acacac!important;
    font-weight: 600!important;
}

th a {
    color: #777!important;
    font-weight: 600!important;
}

th a:hover {
    color: #E585CF!important;
}

.actions.text-right.dt-not-orderable{
    padding-right: 15px!important;
}

.table-responsive{
    border: 1px solid #eaeaea;
    border-radius: 5px;
}

#search-input .select2-selection {
    border:none!important;
}

.btn i{
    margin-right: 3px;
}

hr {
    margin-top: 15px!important;
    margin-bottom: 15px!important;
}

 .voyager .navbar .navbar-nav>li:hover>a {
    color: #FFF!important;
}

.voyager .side-menu .navbar-header {
    background: transparent!important;
}

.analytics-container {
    padding: 0px 25px 25px!important;
}

label{
    color: #37474f;
    font-weight: 500;
}

.custom-pages .no-sort.no-click.bread-actions{
    width: 300px;
}

#rememberMeGroup .controls{
    display: flex;
    align-content: center;
}

.remember-me-text{
    padding-top: 9px;
    padding-left: 4px;
}

.video-preview{
    max-width: 400px;
}

.bg-gradient-primary {
    background-image: linear-gradient(310deg,#7928ca,#ff0080);
    background-repeat: repeat-x;
}

.bg-image {
    background-size: cover;
    background-position: center center;
}

.p-0{
    padding:0px!important;;
}

.min-vh-100{
    min-height: 100vh;
}

.impersonate{
    background: #2ecc71!important;
    margin-left: 4px;
}

.reject-withdrawal{
    background: #f56c42!important;
    margin-left: 4px;
    width: 100%;
    text-align:center!important;
}

.approve-withdrawal-button{
    width: 100%;
    text-align:center!important;
}

.display-inline{
    display:inline;
}

/*
BS4/5 like utilities
*/

/* Margin (m) */
.m-0 { margin: 0 !important; }
.m-1 { margin: 4px !important; }
.m-2 { margin: 8px !important; }
.m-3 { margin: 16px !important; }
.m-4 { margin: 24px !important; }
.m-5 { margin: 32px !important; }

/* Margin Top (mt) */
.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 4px !important; }
.mt-2 { margin-top: 8px !important; }
.mt-3 { margin-top: 16px !important; }
.mt-4 { margin-top: 24px !important; }
.mt-5 { margin-top: 32px !important; }

/* Margin Bottom (mb) */
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 4px !important; }
.mb-2 { margin-bottom: 8px !important; }
.mb-3 { margin-bottom: 16px !important; }
.mb-4 { margin-bottom: 24px !important; }
.mb-5 { margin-bottom: 32px !important; }

/* Margin Left (ml) */
.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: 4px !important; }
.ml-2 { margin-left: 8px !important; }
.ml-3 { margin-left: 16px !important; }
.ml-4 { margin-left: 24px !important; }
.ml-5 { margin-left: 32px !important; }

/* Margin Right (mr) */
.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: 4px !important; }
.mr-2 { margin-right: 8px !important; }
.mr-3 { margin-right: 16px !important; }
.mr-4 { margin-right: 24px !important; }
.mr-5 { margin-right: 32px !important; }

/* Margin X-Axis (mx) */
.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-1 { margin-left: 4px !important; margin-right: 4px !important; }
.mx-2 { margin-left: 8px !important; margin-right: 8px !important; }
.mx-3 { margin-left: 16px !important; margin-right: 16px !important; }
.mx-4 { margin-left: 24px !important; margin-right: 24px !important; }
.mx-5 { margin-left: 32px !important; margin-right: 32px !important; }

/* Margin Y-Axis (my) */
.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-1 { margin-top: 4px !important; margin-bottom: 4px !important; }
.my-2 { margin-top: 8px !important; margin-bottom: 8px !important; }
.my-3 { margin-top: 16px !important; margin-bottom: 16px !important; }
.my-4 { margin-top: 24px !important; margin-bottom: 24px !important; }
.my-5 { margin-top: 32px !important; margin-bottom: 32px !important; }

/* Padding (p) */
.p-0 { padding: 0 !important; }
.p-1 { padding: 4px !important; }
.p-2 { padding: 8px !important; }
.p-3 { padding: 16px !important; }
.p-4 { padding: 24px !important; }
.p-5 { padding: 32px !important; }

/* Padding Top (pt) */
.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: 4px !important; }
.pt-2 { padding-top: 8px !important; }
.pt-3 { padding-top: 16px !important; }
.pt-4 { padding-top: 24px !important; }
.pt-5 { padding-top: 32px !important; }

/* Padding Bottom (pb) */
.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: 4px !important; }
.pb-2 { padding-bottom: 8px !important; }
.pb-3 { padding-bottom: 16px !important; }
.pb-4 { padding-bottom: 24px !important; }
.pb-5 { padding-bottom: 32px !important; }

/* Padding Left (pl) */
.pl-0 { padding-left: 0 !important; }
.pl-1 { padding-left: 4px !important; }
.pl-2 { padding-left: 8px !important; }
.pl-3 { padding-left: 16px !important; }
.pl-4 { padding-left: 24px !important; }
.pl-5 { padding-left: 32px !important; }

/* Padding Right (pr) */
.pr-0 { padding-right: 0 !important; }
.pr-1 { padding-right: 4px !important; }
.pr-2 { padding-right: 8px !important; }
.pr-3 { padding-right: 16px !important; }
.pr-4 { padding-right: 24px !important; }
.pr-5 { padding-right: 32px !important; }

/* Padding X-Axis (px) */
.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-1 { padding-left: 4px !important; padding-right: 4px !important; }
.px-2 { padding-left: 8px !important; padding-right: 8px !important; }
.px-3 { padding-left: 16px !important; padding-right: 16px !important; }
.px-4 { padding-left: 24px !important; padding-right: 24px !important; }
.px-5 { padding-left: 32px !important; padding-right: 32px !important; }

/* Padding Y-Axis (py) */
.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-1 { padding-top: 4px !important; padding-bottom: 4px !important; }
.py-2 { padding-top: 8px !important; padding-bottom: 8px !important; }
.py-3 { padding-top: 16px !important; padding-bottom: 16px !important; }
.py-4 { padding-top: 24px !important; padding-bottom: 24px !important; }
.py-5 { padding-top: 32px !important; padding-bottom: 32px !important; }

/* Font Size */
.fs-1 { font-size: 2.5rem !important; } /* Equivalent to something like 40px */
.fs-2 { font-size: 2rem !important; }   /* Equivalent to something like 32px */
.fs-3 { font-size: 1.75rem !important; } /* Equivalent to something like 28px */
.fs-4 { font-size: 1.5rem !important; }  /* Equivalent to something like 24px */
.fs-5 { font-size: 1.20rem !important; } /* Equivalent to something like 20px */
.fs-6 { font-size: 1rem !important; }    /* Equivalent to something like 16px */
.fs-7 { font-size: 0.875rem !important; }/* Equivalent to something like 14px */
.fs-8 { font-size: 0.75rem !important; } /* Equivalent to something like 12px */
.fs-9 { font-size: 0.625rem !important; }/* Equivalent to something like 10px */

.w-10{
    width: 10px;
    height: 10px;
}
