/*!
*
* Laravel Big bang default theme with light - dark alternatives
*
 */

@import "../../node_modules/bootstrap/scss/functions";
@import "./bootstrap/dark/functions";

@import "./variables";  // Base theme variables
@import "../../node_modules/bootstrap/scss/variables";
@import "./variables-dark";  // Dark theme colors override | only the dark colors mapped as `-alt`
$color-scheme-alt: dark;
$isRTL : false;

@import "../../node_modules/bootstrap/scss/mixins";

@import "./bootstrap/typography";
@import "./bootstrap/gradients";

@import "../../node_modules/bootstrap/scss/root";
@import "../../node_modules/bootstrap/scss/reboot";
@import "../../node_modules/bootstrap/scss/type";
@import "../../node_modules/bootstrap/scss/images";
@import "../../node_modules/bootstrap/scss/code";
@import "../../node_modules/bootstrap/scss/grid";
@import "../../node_modules/bootstrap/scss/tables";
@import "../../node_modules/bootstrap/scss/forms";
@import "../../node_modules/bootstrap/scss/buttons";
@import "./bootstrap/buttons";
@import "../../node_modules/bootstrap/scss/transitions";
@import "./bootstrap/dropdown";
@import "../../node_modules/bootstrap/scss/dropdown";
@import "../../node_modules/bootstrap/scss/button-group";
@import "../../node_modules/bootstrap/scss/input-group";
@import "../../node_modules/bootstrap/scss/custom-forms";
@import "../../node_modules/bootstrap/scss/nav";
@import "../../node_modules/bootstrap/scss/navbar";
@import "../../node_modules/bootstrap/scss/card";
@import "../../node_modules/bootstrap/scss/breadcrumb";
@import "../../node_modules/bootstrap/scss/pagination";
@import "../../node_modules/bootstrap/scss/badge";
@import "./bootstrap/badge";
@import "../../node_modules/bootstrap/scss/alert";
@import "./bootstrap/alert";
@import "../../node_modules/bootstrap/scss/progress";
@import "../../node_modules/bootstrap/scss/media";
@import "../../node_modules/bootstrap/scss/list-group";
@import "../../node_modules/bootstrap/scss/close";
@import "../../node_modules/bootstrap/scss/toasts";
@import "../../node_modules/bootstrap/scss/modal";
@import "../../node_modules/bootstrap/scss/tooltip";
@import "../../node_modules/bootstrap/scss/popover";
@import "../../node_modules/bootstrap/scss/carousel";
@import "../../node_modules/bootstrap/scss/spinners";
@import "../../node_modules/bootstrap/scss/utilities";
@import "./bootstrap/utilities";
@import "../../node_modules/bootstrap/scss/print";

// Preloading elements
@import "../../node_modules/placeholder-loading/src/scss/placeholder-loading";

footer .copyRightInfo .nav-link:nth-last-child(1){
    padding-right:0px!important;
}

footer .footer-social-links a:nth-last-child(1){
    margin-right:0px!important;
}

// TODO: Fix contrast issues on some themes
//$yiq-contrasted-threshold:  150 !default;

@import "./generic";
