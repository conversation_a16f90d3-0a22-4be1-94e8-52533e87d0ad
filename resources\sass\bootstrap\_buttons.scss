
.btn {
    margin-bottom: $btn-margin-bottom;
    letter-spacing: $a-letter-spacing;
    text-transform: uppercase;
    box-shadow: $btn-box-shadow;
    background-size: $btn-background-size;
    background-position-x: $btn-background-position-x;

    &:not([class*="btn-outline-"]) {
        border: 0;
    }
}

.icon-move-right {
    i {
        transition: $btn-icon-transition;
    }
    &:hover,
    &:focus {
        i {
            transform: $btn-icon-transform-right;
        }
    }
}

.icon-move-left{
    i {
        transition: $btn-icon-transition;
    }
    &:hover,
    &:focus {
        i {
            transform: $btn-icon-transform-left;
        }
    }
}

@each $color, $value in $theme-colors {
    .btn.bg-gradient-#{$color} {
        @include hover {
            background-color: $value;
            border-color: $value;
        }
        .btn.bg-outline-#{$color} {
            border: $border-width solid $value;
        }

        &:not(:disabled):not(.disabled).active,
        &:not(:disabled):not(.disabled):active,
        .show > &.dropdown-toggle {
            color: color-yiq($value);
            background-color: $value;
        }

        @if $value != $white and $value != $light {
            &.focus,
            &:focus {
                color: $white;
            }
        }
    }

    // .btn-#{$color},
    // .btn.bg-gradient-#{$color}{
    //   &:focus{
    //     box-shadow: 0 0 0 3px rgba($value,.5);
    //   }
    // }

    .btn-outline-#{$color} {
        box-shadow: none;
        @include hover {
            &:not(.active){
                background-color: transparent;
                opacity: .75;
                box-shadow: none;
                color: $value;
            }
        }
    }
}

.btn-outline-white{
    border-color: rgba($white, .75);
    background: rgba($white, .1);
}

@each $color, $value in $btn-font-colors {
    .btn.bg-gradient-#{$color} {
        color: $value;
        @include hover {
            color: $value;
        }
    }
}

// buttons box-shadow

@each $color, $value in $theme-colors {
    .btn-#{$color},
    .btn.bg-gradient-#{$color} {
        //box-shadow: $btn-box-shadow-values rgba($value, .2);

        @include hover {
            //box-shadow: $btn-box-shadow-hover-values rgba($value, .2);
        }
    }
}


.btn-outline-white{
    border-color: rgba($white, .75);
    background: rgba($white, .1);
}

// "Hardcoded" fix
.btn-success{
    color: #fff!important;
}
.btn-success.focus, .btn-success:focus, .btn.bg-gradient-success.focus, .btn.bg-gradient-success:focus {
    color: #fff!important;
}


.btn-grow{
    transition: all .2s ease-in-out;

}

.btn-grow:hover{
    transform: scale(1.02);
}

.btn.btn-round {
    border-radius: 1.875rem;
}
