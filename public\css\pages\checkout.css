.rounded {
    border-radius: 1rem
}

.nav-pills .nav-link {
    color: #555
}

.nav-pills .nav-link.active {
    color: white
}

input[type="radio"] {
    margin-right: 5px
}

.radio {
    display: inline-block;
    border-radius: 6px;
    box-sizing: border-box;
    border: 2px solid lightgrey;
    cursor: pointer;
    margin: 6px 25px 6px 0px
}

.radio:hover {
    box-shadow: 0px 0px 0px 1px rgba(0, 0, 0, 0.2)
}

.radio.selected {
    box-shadow: 0px 0px 0px 1px #9b0979;
    border: 2px solid #9b0979
}

.label-radio {
    font-weight: bold;
    color: #000000
}

.checkout-payment-provider {
    background: #ffffff;
    height: 59px;
    text-align: center;
}

.coinbase-payment-provider img, .paypal-payment-provider img, .ccbill-payment-provider img, .paystack-payment-provider img, .oxxo-payment-provider img, .mercado-payment-provider img{
    width:90px
}

.nowpayments-payment-provider img{
    width:83px;
}

.credit-provider-text {
    color: #000000;
    margin: auto;
}

#billing-agreement-form label.error {
    color: #ea0606!important;
}
