/*!
*
* Laravel Big bang default theme with light - dark alternatives
*
 */

@import "../../node_modules/bootstrap/scss/functions";
@import "./bootstrap/dark/functions";

@import "./variables";  // Base theme variables
@import "../../node_modules/bootstrap/scss/variables";
@import "./variables-dark";  // Dark theme colors override | only the dark colors mapped as `-alt`
$color-scheme-alt: light;
$isRTL : true;

@import "../../node_modules/bootstrap/scss/mixins";
@import "./bootstrap/dark/mixins";

@import "./bootstrap/dark/utilities/color-schemes";
@import "./bootstrap/typography";
@import "./bootstrap/gradients";

@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/variables";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/reboot-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/type-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/images";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/code";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/grid";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/tables";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/forms-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/buttons";
@import "./bootstrap/buttons";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/transitions";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/dropdown-rtl";
@import "./bootstrap/dropdown";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/button-group-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/input-group-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/custom-forms-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/nav-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/navbar-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/card-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/breadcrumb-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/pagination-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/badge";
@import "./bootstrap/badge";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/alert-rtl";
@import "./bootstrap/alert";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/progress";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/media";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/list-group-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/close-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/toasts";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/modal-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/tooltip";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/popover-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/carousel-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/spinners-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/utilities";
@import "./bootstrap/utilities";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/print";

    @import "./bootstrap/dark/root";
    @import "./bootstrap/dark/reboot";
    @import "./bootstrap/dark/type";
    @import "./bootstrap/dark/images";
    @import "./bootstrap/dark/code";
    @import "./bootstrap/dark/tables";
    @import "./bootstrap/dark/forms";
    @import "./bootstrap/dark/buttons";
    // _transitions not used
    @import "./bootstrap/dark/dropdown";
    @import "./bootstrap/dark/button-group";
    @import "./bootstrap/dark/input-group";
    @import "./bootstrap/dark/custom-forms";
    @import "./bootstrap/dark/nav";
    @import "./bootstrap/dark/navbar";
    @import "./bootstrap/dark/card";
    @import "./bootstrap/dark/breadcrumb";
    @import "./bootstrap/dark/pagination";
    @import "./bootstrap/dark/badge";
    @import "./bootstrap/dark/alert";
    @import "./bootstrap/dark/progress";
    // _media not used
    @import "./bootstrap/dark/list-group";
    @import "./bootstrap/dark/close";
    @import "./bootstrap/dark/toasts";
    @import "./bootstrap/dark/modal";
    @import "./bootstrap/dark/tooltip";
    @import "./bootstrap/dark/popover";
    @import "./bootstrap/dark/carousel";
    // _spinners not used
    @import "./bootstrap/dark/utilities";
    @import "./bootstrap/dark/dark";

@import "./bootstrap/dark/utilities/display-dark";

// Preloading Elements
$ph-direction:            rtl !default;
$ph-bg:                   lighten($gray-900-alt,8%) !default;
$ph-color:                darken($gray-800-alt,8%) !default;
@import "../../node_modules/placeholder-loading/src/scss/placeholder-loading";

footer .copyRightInfo .nav-link:nth-last-child(1){
    padding-left:0px!important;
    margin-right: 1rem!important;
}

footer .footer-social-links a:nth-last-child(1){
    margin-left:0px!important;
    margin-right: 0.5rem!important;
}


@import "./generic";
