{"name": "theqdev/justfans", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "anhskohbo/no-captcha": "^3.4", "devfactory/minify": "1.0.*", "graham-campbell/markdown": "14.0", "guzzlehttp/guzzle": "^7.0.1", "intervention/image": "^2.7", "jenssegers/agent": "^2.6", "laracasts/utilities": "^3.2", "laravel/framework": "^9.19", "laravel/socialite": "^5.3", "laravel/tinker": "^2.5", "laravel/ui": "^3.0", "league/flysystem-aws-s3-v3": "^3.0", "mercadopago/dx-php": "^2.6", "mews/purifier": "^3.3", "paypal/rest-api-sdk-php": "^1.14", "pbmedia/laravel-ffmpeg": "^8.2.2", "pion/laravel-chunk-upload": "^1.4", "pusher/pusher-php-server": "^5.0", "ramsey/uuid": "^4.2.2", "silviolleite/laravelpwa": "^2.0", "spatie/schema-org": "^3.9", "stripe/stripe-php": "^7.77", "symfony/http-client": "^6.0", "symfony/mailgun-mailer": "^6.0", "tcg/voyager": "^1.5", "yabacon/paystack-php": "^2.2", "zanysoft/laravel-zip": "^2.0", "opencoconut/coconut": "3.*"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.5", "fakerphp/faker": "^1.9.1", "friendsofphp/php-cs-fixer": "^3.0", "mockery/mockery": "^1.0", "nunomaduro/collision": "^6.1", "orangehill/iseed": "^3.0", "phpmd/phpmd": "^2.15", "phpunit/phpunit": "^9.0", "spatie/laravel-ignition": "^1.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform": {"php": "8.0.2"}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "classmap": ["database/seeders", "database/factories", "app/Model"], "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "repositories": {"hooks": {"type": "composer", "url": "https://larapack.io"}}}