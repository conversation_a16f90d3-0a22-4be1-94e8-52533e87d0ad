<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class InstallerSaveAdminInfoRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'site_title' => 'required',
            'app_url' => 'required',
            'email' => 'required|email|max:255',
            'password' => 'required|min:6',
            'license' => 'required',
        ];
    }
}
