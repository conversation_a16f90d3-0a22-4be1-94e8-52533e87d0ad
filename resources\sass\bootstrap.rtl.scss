/*!
*
* Laravel Big bang default theme with light - dark alternatives
*
 */
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/functions";
@import "./bootstrap/dark/functions";

@import "./variables";  // Base theme variables
@import "../../node_modules/bootstrap/scss/variables";
@import "./variables-dark";  // Dark theme colors override | only the dark colors mapped as `-alt`
$color-scheme-alt: dark;
$isRTL : true;

@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/mixins";
@import "./bootstrap/typography";
@import "./bootstrap/gradients";

@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/root";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/reboot-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/type-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/images";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/code";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/grid";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/tables";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/forms-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/buttons";
@import "./bootstrap/buttons";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/transitions";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/dropdown-rtl";
@import "./bootstrap/dropdown";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/button-group-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/input-group-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/custom-forms-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/nav-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/navbar-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/card-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/breadcrumb-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/pagination-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/badge";
@import "./bootstrap/badge";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/alert-rtl";
@import "./bootstrap/alert";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/progress";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/media";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/list-group-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/close-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/toasts";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/modal-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/tooltip";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/popover-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/carousel-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/spinners-rtl";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/utilities";
@import "./bootstrap/utilities";
@import "../../node_modules/@laylazi/bootstrap-rtl-scss/scss/print";

// Preloading Elements
$ph-direction:            rtl !default;
$ph-bg:                   #fff !default;
$ph-color:                #ced4da !default;
@import "../../node_modules/placeholder-loading/src/scss/placeholder-loading";

@import "./generic";

.list-item .user-avatar:last-child{
    margin-left:0px;
}

footer .copyRightInfo .nav-link:nth-last-child(1){
    padding-left:0px!important;
    margin-right: 1rem!important;
}

footer .footer-social-links a:nth-last-child(1){
    margin-left:0px!important;
    margin-right: 0.5rem!important;
}
