<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Str;

class Sluggable implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if(Str::slug($value)){
            return true;
        }
        return false;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return __('Stream url can not be generated. Please enter at least one alphanumeric character in the stream title.');
    }
}
