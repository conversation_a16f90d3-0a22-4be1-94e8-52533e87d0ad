.table-wrapper{}

.profileAvatar-settings {
    width: 4rem;
    height: 4rem;
}

.nav{
    flex-wrap:nowrap;
}

.verify-form .dropzone{
    min-height: auto;!important;
}

.verify-form  .dropzone .dz-preview.dz-image-preview {
    background: transparent!important;
}

.verify-form  .dropzone .dz-preview:hover .dz-image img {
    -webkit-filter: blur(2px)!important;
    filter: blur(2px)!important;
}

.verify-form  .dropzone .dz-preview .dz-filename{
    display: none!important;
}

.verify-form  .dropzone .dz-preview {
    min-height: unset;
}

.manual-payment-uploader {
    min-height: 100px!important;
}

.profile-cover-bg .h-pill, .avatar-holder .h-pill
{
    width: 30px;
    height: 30px;
}

@media (max-width: 767px) {
    .settings-content {
        border:none!important;
    }
}

.dz-filename{
    display: none;
}

.fa-details-label{
    margin-left: -36px;
}


.table-wrapper .rounded-circle.user-avatar{
    width: 36px;
    height: 36px;
}

.CodeMirror, .CodeMirror-scroll {
    min-height: 100px;
}
