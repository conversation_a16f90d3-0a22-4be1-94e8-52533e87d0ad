.conversation-content{
    height:100vh;
    overflow-x: hidden;
    overflow-y: auto;
}

.conversation-wrapper{
    height: 250px;
}

.conversation-header{}

.conversation-header .details-holder{
    padding: 30px 15px 15px;
}

.conversation-header .details-holder img{
    width:40px;
    height: 40px;
    border-radius: 50px;
}

.conversation-content{
    height: 250px;
    overflow-x: hidden;
}

.conversation-writeup textarea{}

.messageBoxInput{
    /*height:45px!important;*/
    min-height: 45px!important;
    max-height: 120px!important;
    overflow-y: hidden;
    word-wrap:break-word;
    border-radius: 20px;
}

.messenger-buttons-wrapper{}


/* custom scrollbar */
.conversation-content::-webkit-scrollbar {
    width: 20px;
}

.conversation-content::-webkit-scrollbar-track {
    background-color: transparent;
}

.conversation-content::-webkit-scrollbar-thumb {
    background-color: #C1C1C1;
    border-radius: 20px;
    border: 6px solid transparent;
    background-clip: content-box;
}

.conversation-content::-webkit-scrollbar-thumb:hover {
    background-color: #A8A8A8;
}

.messageBoxInput{
    height:45px
}

.chat-message-user a, .chat-message-user a:hover{
    color:inherit;
}

.chat-message-user a:hover{
    font-weight:700;
}

.profile-cover-bg{
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.stream-details .avatar{
    width:64px;
    height:64px;
}

@media (min-width: 767px) {
    .stream-locked{
        padding-left: 150px;
        padding-right: 150px;
    }

    @-moz-document url-prefix() {
        .stream-locked {
            padding-left: 0px;
            padding-right: 0px;
        }
    }

}
