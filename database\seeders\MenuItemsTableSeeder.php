<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class MenuItemsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('menu_items')->delete();
        
        \DB::table('menu_items')->insert(array (
            0 => 
            array (
                'id' => 1,
                'menu_id' => 1,
                'title' => 'Dashboard',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-home',
                'color' => '#000000',
                'parent_id' => NULL,
                'order' => 1,
                'created_at' => '2021-08-07 18:52:09',
                'updated_at' => '2022-06-24 13:14:52',
                'route' => 'voyager.dashboard',
                'parameters' => 'null',
            ),
            1 => 
            array (
                'id' => 2,
                'menu_id' => 1,
                'title' => 'Media',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-images',
                'color' => NULL,
                'parent_id' => NULL,
                'order' => 2,
                'created_at' => '2021-08-07 18:52:09',
                'updated_at' => '2021-08-08 22:32:02',
                'route' => 'voyager.media.index',
                'parameters' => NULL,
            ),
            2 => 
            array (
                'id' => 3,
                'menu_id' => 1,
                'title' => 'Users',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-person',
                'color' => NULL,
                'parent_id' => 29,
                'order' => 1,
                'created_at' => '2021-08-07 18:52:09',
                'updated_at' => '2021-08-07 20:59:55',
                'route' => 'voyager.users.index',
                'parameters' => NULL,
            ),
            3 => 
            array (
                'id' => 4,
                'menu_id' => 1,
                'title' => 'Roles',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-lock',
                'color' => NULL,
                'parent_id' => 29,
                'order' => 3,
                'created_at' => '2021-08-07 18:52:09',
                'updated_at' => '2021-10-20 16:20:11',
                'route' => 'voyager.roles.index',
                'parameters' => NULL,
            ),
            4 => 
            array (
                'id' => 6,
                'menu_id' => 1,
                'title' => 'Menu Builder',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-list',
                'color' => NULL,
                'parent_id' => 5,
                'order' => 1,
                'created_at' => '2021-08-07 18:52:09',
                'updated_at' => '2021-08-07 20:12:22',
                'route' => 'voyager.menus.index',
                'parameters' => NULL,
            ),
            5 => 
            array (
                'id' => 7,
                'menu_id' => 1,
                'title' => 'Database',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-data',
                'color' => NULL,
                'parent_id' => 5,
                'order' => 2,
                'created_at' => '2021-08-07 18:52:09',
                'updated_at' => '2021-08-07 20:12:22',
                'route' => 'voyager.database.index',
                'parameters' => NULL,
            ),
            6 => 
            array (
                'id' => 8,
                'menu_id' => 1,
                'title' => 'Compass',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-compass',
                'color' => NULL,
                'parent_id' => 5,
                'order' => 3,
                'created_at' => '2021-08-07 18:52:09',
                'updated_at' => '2021-08-07 20:12:22',
                'route' => 'voyager.compass.index',
                'parameters' => NULL,
            ),
            7 => 
            array (
                'id' => 9,
                'menu_id' => 1,
                'title' => 'BREAD',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-bread',
                'color' => NULL,
                'parent_id' => 5,
                'order' => 4,
                'created_at' => '2021-08-07 18:52:09',
                'updated_at' => '2021-08-07 20:12:22',
                'route' => 'voyager.bread.index',
                'parameters' => NULL,
            ),
            8 => 
            array (
                'id' => 10,
                'menu_id' => 1,
                'title' => 'Settings',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-settings',
                'color' => NULL,
                'parent_id' => NULL,
                'order' => 12,
                'created_at' => '2021-08-07 18:52:09',
                'updated_at' => '2024-08-07 17:34:31',
                'route' => 'voyager.settings.index',
                'parameters' => NULL,
            ),
            9 => 
            array (
                'id' => 12,
                'menu_id' => 1,
                'title' => 'Wallets',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-wallet',
                'color' => '#000000',
                'parent_id' => 29,
                'order' => 4,
                'created_at' => '2021-08-07 19:37:16',
                'updated_at' => '2021-10-20 16:20:11',
                'route' => 'voyager.wallets.index',
                'parameters' => 'null',
            ),
            10 => 
            array (
                'id' => 14,
                'menu_id' => 1,
                'title' => 'Attachments',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-paperclip',
                'color' => '#000000',
                'parent_id' => 28,
                'order' => 2,
                'created_at' => '2021-08-07 20:16:55',
                'updated_at' => '2022-02-01 15:41:20',
                'route' => 'voyager.attachments.index',
                'parameters' => 'null',
            ),
            11 => 
            array (
                'id' => 15,
                'menu_id' => 1,
                'title' => 'Notifications',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-bell',
                'color' => NULL,
                'parent_id' => 29,
                'order' => 5,
                'created_at' => '2021-08-07 20:19:11',
                'updated_at' => '2021-10-20 16:20:11',
                'route' => 'voyager.notifications.index',
                'parameters' => NULL,
            ),
            12 => 
            array (
                'id' => 16,
                'menu_id' => 1,
                'title' => 'Post Comments',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-bubble',
                'color' => NULL,
                'parent_id' => 28,
                'order' => 3,
                'created_at' => '2021-08-07 20:20:55',
                'updated_at' => '2022-02-01 15:41:20',
                'route' => 'voyager.post-comments.index',
                'parameters' => NULL,
            ),
            13 => 
            array (
                'id' => 17,
                'menu_id' => 1,
                'title' => 'Posts',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-images',
                'color' => NULL,
                'parent_id' => 28,
                'order' => 1,
                'created_at' => '2021-08-07 20:22:37',
                'updated_at' => '2021-08-07 20:58:22',
                'route' => 'voyager.user-posts.index',
                'parameters' => NULL,
            ),
            14 => 
            array (
                'id' => 18,
                'menu_id' => 1,
                'title' => 'Reactions',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-bubble-hear',
                'color' => NULL,
                'parent_id' => 29,
                'order' => 7,
                'created_at' => '2021-08-07 20:24:58',
                'updated_at' => '2021-10-20 16:20:11',
                'route' => 'voyager.reactions.index',
                'parameters' => NULL,
            ),
            15 => 
            array (
                'id' => 19,
                'menu_id' => 1,
                'title' => 'Subscriptions',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-credit-cards',
                'color' => NULL,
                'parent_id' => 27,
                'order' => 1,
                'created_at' => '2021-08-07 20:25:32',
                'updated_at' => '2021-08-07 20:55:55',
                'route' => 'voyager.subscriptions.index',
                'parameters' => NULL,
            ),
            16 => 
            array (
                'id' => 20,
                'menu_id' => 1,
                'title' => 'Transactions',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-dollar',
                'color' => NULL,
                'parent_id' => 27,
                'order' => 2,
                'created_at' => '2021-08-07 20:26:33',
                'updated_at' => '2021-08-07 20:55:55',
                'route' => 'voyager.transactions.index',
                'parameters' => NULL,
            ),
            17 => 
            array (
                'id' => 21,
                'menu_id' => 1,
                'title' => 'User Bookmarks',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-bookmark',
                'color' => NULL,
                'parent_id' => 28,
                'order' => 4,
                'created_at' => '2021-08-07 20:27:47',
                'updated_at' => '2022-02-01 15:41:20',
                'route' => 'voyager.user-bookmarks.index',
                'parameters' => NULL,
            ),
            18 => 
            array (
                'id' => 22,
                'menu_id' => 1,
                'title' => 'Lists',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-list',
                'color' => '#000000',
                'parent_id' => 29,
                'order' => 8,
                'created_at' => '2021-08-07 20:28:45',
                'updated_at' => '2021-10-20 16:20:11',
                'route' => 'voyager.user-lists.index',
                'parameters' => 'null',
            ),
            19 => 
            array (
                'id' => 23,
                'menu_id' => 1,
                'title' => 'List Members',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-people',
                'color' => '#000000',
                'parent_id' => 29,
                'order' => 9,
                'created_at' => '2021-08-07 20:29:07',
                'updated_at' => '2021-10-20 16:19:58',
                'route' => 'voyager.user-list-members.index',
                'parameters' => 'null',
            ),
            20 => 
            array (
                'id' => 24,
                'menu_id' => 1,
                'title' => 'Messages',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-chat',
                'color' => '#000000',
                'parent_id' => 29,
                'order' => 6,
                'created_at' => '2021-08-07 20:42:32',
                'updated_at' => '2021-10-20 16:20:11',
                'route' => 'voyager.user-messages.index',
                'parameters' => 'null',
            ),
            21 => 
            array (
                'id' => 25,
                'menu_id' => 1,
                'title' => 'Withdrawals',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-calendar',
                'color' => NULL,
                'parent_id' => 27,
                'order' => 3,
                'created_at' => '2021-08-07 20:51:14',
                'updated_at' => '2021-08-07 20:55:53',
                'route' => 'voyager.withdrawals.index',
                'parameters' => NULL,
            ),
            22 => 
            array (
                'id' => 26,
                'menu_id' => 1,
                'title' => 'User lists',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-list',
                'color' => '#000000',
                'parent_id' => NULL,
                'order' => 4,
                'created_at' => '2021-08-07 20:54:21',
                'updated_at' => '2021-09-30 11:21:46',
                'route' => NULL,
                'parameters' => '',
            ),
            23 => 
            array (
                'id' => 27,
                'menu_id' => 1,
                'title' => 'Money',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-dollar',
                'color' => '#000000',
                'parent_id' => NULL,
                'order' => 7,
                'created_at' => '2021-08-07 20:55:37',
                'updated_at' => '2023-06-21 15:02:27',
                'route' => NULL,
                'parameters' => '',
            ),
            24 => 
            array (
                'id' => 28,
                'menu_id' => 1,
                'title' => 'Posts',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-images',
                'color' => '#000000',
                'parent_id' => NULL,
                'order' => 5,
                'created_at' => '2021-08-07 20:57:03',
                'updated_at' => '2021-09-30 11:21:46',
                'route' => NULL,
                'parameters' => '',
            ),
            25 => 
            array (
                'id' => 29,
                'menu_id' => 1,
                'title' => 'Users',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-person',
                'color' => '#000000',
                'parent_id' => NULL,
                'order' => 3,
                'created_at' => '2021-08-07 20:58:37',
                'updated_at' => '2021-08-08 22:32:02',
                'route' => NULL,
                'parameters' => '',
            ),
            26 => 
            array (
                'id' => 30,
                'menu_id' => 1,
                'title' => 'Countries',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-location',
                'color' => '#000000',
                'parent_id' => 33,
                'order' => 2,
                'created_at' => '2021-09-21 18:10:16',
                'updated_at' => '2021-09-30 11:26:53',
                'route' => 'voyager.countries.index',
                'parameters' => 'null',
            ),
            27 => 
            array (
                'id' => 31,
                'menu_id' => 1,
                'title' => 'Taxes',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-credit-card',
                'color' => '#000000',
                'parent_id' => 33,
                'order' => 1,
                'created_at' => '2021-09-21 18:11:55',
                'updated_at' => '2021-09-30 11:26:53',
                'route' => 'voyager.taxes.index',
                'parameters' => 'null',
            ),
            28 => 
            array (
                'id' => 32,
                'menu_id' => 1,
                'title' => 'Pages',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-news',
                'color' => '#000000',
                'parent_id' => NULL,
                'order' => 9,
                'created_at' => '2021-09-29 19:43:27',
                'updated_at' => '2023-06-21 15:02:27',
                'route' => 'voyager.custom-pages.index',
                'parameters' => 'null',
            ),
            29 => 
            array (
                'id' => 33,
                'menu_id' => 1,
                'title' => 'Taxes',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-credit-card',
                'color' => '#000000',
                'parent_id' => NULL,
                'order' => 8,
                'created_at' => '2021-09-30 11:25:21',
                'updated_at' => '2023-06-21 15:02:27',
                'route' => NULL,
                'parameters' => '',
            ),
            30 => 
            array (
                'id' => 34,
                'menu_id' => 1,
                'title' => 'Identity Checks',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-check',
                'color' => '#000000',
                'parent_id' => 29,
                'order' => 2,
                'created_at' => '2021-10-20 16:11:44',
                'updated_at' => '2021-10-20 16:21:40',
                'route' => 'voyager.user-verifies.index',
                'parameters' => 'null',
            ),
            31 => 
            array (
                'id' => 35,
                'menu_id' => 1,
                'title' => 'User Reports',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-eye',
                'color' => '#000000',
                'parent_id' => 29,
                'order' => 10,
                'created_at' => '2021-11-05 11:32:40',
                'updated_at' => '2022-02-01 15:41:22',
                'route' => 'voyager.user-reports.index',
                'parameters' => 'null',
            ),
            32 => 
            array (
                'id' => 36,
                'menu_id' => 1,
                'title' => 'Contact Messages',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-book',
                'color' => '#000000',
                'parent_id' => NULL,
                'order' => 10,
                'created_at' => '2021-11-19 18:11:34',
                'updated_at' => '2024-08-07 17:37:08',
                'route' => 'voyager.contact-messages.index',
                'parameters' => 'null',
            ),
            33 => 
            array (
                'id' => 37,
                'menu_id' => 1,
                'title' => 'Featured Users',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-star',
                'color' => '#000000',
                'parent_id' => 29,
                'order' => 11,
                'created_at' => '2022-02-01 15:00:11',
                'updated_at' => '2022-02-01 15:41:54',
                'route' => 'voyager.featured-users.index',
                'parameters' => 'null',
            ),
            34 => 
            array (
                'id' => 38,
                'menu_id' => 1,
                'title' => 'Payment Requests',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-window-list',
                'color' => '#000000',
                'parent_id' => 27,
                'order' => 4,
                'created_at' => '2022-02-06 16:23:24',
                'updated_at' => '2022-02-06 16:25:11',
                'route' => 'voyager.payment-requests.index',
                'parameters' => 'null',
            ),
            35 => 
            array (
                'id' => 39,
                'menu_id' => 1,
                'title' => 'Invoices',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-receipt',
                'color' => NULL,
                'parent_id' => 27,
                'order' => 5,
                'created_at' => '2022-08-04 19:06:47',
                'updated_at' => '2022-08-04 19:07:16',
                'route' => 'voyager.invoices.index',
                'parameters' => NULL,
            ),
            36 => 
            array (
                'id' => 41,
                'menu_id' => 1,
                'title' => 'Stream Messages',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-chat',
                'color' => NULL,
                'parent_id' => 42,
                'order' => 2,
                'created_at' => '2023-06-21 14:48:56',
                'updated_at' => '2023-06-21 15:02:27',
                'route' => 'voyager.stream-messages.index',
                'parameters' => NULL,
            ),
            37 => 
            array (
                'id' => 42,
                'menu_id' => 1,
                'title' => 'Streams',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-video',
                'color' => '#000000',
                'parent_id' => NULL,
                'order' => 6,
                'created_at' => '2023-06-21 14:56:54',
                'updated_at' => '2023-06-21 15:02:27',
                'route' => NULL,
                'parameters' => '',
            ),
            38 => 
            array (
                'id' => 43,
                'menu_id' => 1,
                'title' => 'Streams',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-video',
                'color' => '#000000',
                'parent_id' => 42,
                'order' => 1,
                'created_at' => '2023-06-21 15:01:44',
                'updated_at' => '2023-06-21 15:03:03',
                'route' => 'voyager.streams.index',
                'parameters' => 'null',
            ),
            39 => 
            array (
                'id' => 46,
                'menu_id' => 1,
                'title' => 'Referrals',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-group',
                'color' => '#000000',
                'parent_id' => 27,
                'order' => 6,
                'created_at' => '2023-06-21 16:26:04',
                'updated_at' => '2023-06-21 16:31:29',
                'route' => 'voyager.rewards.index',
                'parameters' => 'null',
            ),
            40 => 
            array (
                'id' => 47,
                'menu_id' => 1,
                'title' => 'Announcements',
                'url' => '',
                'target' => '_self',
                'icon_class' => 'voyager-megaphone',
                'color' => '#000000',
                'parent_id' => NULL,
                'order' => 11,
                'created_at' => '2024-08-07 17:27:57',
                'updated_at' => '2024-08-07 17:37:08',
                'route' => 'voyager.global-announcements.index',
                'parameters' => 'null',
            ),
        ));
        
        
    }
}