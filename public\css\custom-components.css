/* Custom Component Styles - Additional CSS for Vue Components */

/* Neighborhood Chip Component */
.neighborhood-chip[data-v-69f6020a] {
    align-items: center;
    border-radius: 9999px;
    display: inline-flex;
    gap: .5rem;
    margin-right: .5rem;
    --tw-bg-opacity: 1;
    background-color: rgb(38 50 56/var(--tw-bg-opacity));
    padding: 6px .75rem
}

.neighborhood-chip p[data-v-69f6020a] {
    font-family: Montserrat,sans-serif;
    font-size: .875rem;
    font-weight: 600;
    line-height: 1.25rem;
    margin: 0;
    padding: 0;
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.neighborhood-chip__close[data-v-69f6020a] {
    align-items: center;
    border-radius: 9999px;
    cursor: pointer;
    display: flex;
    height: 1rem;
    justify-content: center;
    width: 1rem;
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

/* Header Input Component */
.icon-pointer[data-v-69dca9ee] {
    cursor: pointer
}

.header-input[data-v-69dca9ee] {
    align-items: center;
    border-radius: 9999px;
    border-style: solid;
    border-width: 1px;
    display: flex;
    --tw-border-opacity: 1;
    border-color: rgb(202 212 216/var(--tw-border-opacity));
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity));
    font-family: Montserrat,sans-serif;
    padding-left: .75rem;
    padding-right: .75rem
}

.header-input--focused[data-v-69dca9ee] {
    --tw-border-opacity: 1;
    border-color: rgb(38 50 56/var(--tw-border-opacity))
}

.header-input input[data-v-69dca9ee] {
    background-color: transparent;
    border-style: none;
    flex: 1 1 0%;
    font-size: .875rem;
    height: 100%;
    line-height: 1.25rem;
    padding: 14px .75rem;
    --tw-text-opacity: 1;
    color: rgb(38 50 56/var(--tw-text-opacity));
    outline: 2px solid transparent;
    outline-offset: 2px
}

.header-input input[data-v-69dca9ee]::-moz-placeholder {
    --tw-text-opacity: 1;
    color: rgb(127 138 148/var(--tw-text-opacity))
}

.header-input input[data-v-69dca9ee]::placeholder {
    --tw-text-opacity: 1;
    color: rgb(127 138 148/var(--tw-text-opacity))
}

.header-input__counter[data-v-69dca9ee] {
    align-items: center;
    border-radius: 1.5rem;
    display: flex;
    justify-content: center;
    --tw-bg-opacity: 1;
    background-color: rgb(38 50 56/var(--tw-bg-opacity));
    font-family: Montserrat,sans-serif;
    font-size: .875rem;
    font-weight: 600;
    line-height: 1.25rem;
    padding: 6px .75rem;
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

/* Location Search Modal Component */
.location-search-modal-header[data-v-1e5da366] {
    display: flex;
    flex-direction: column;
    gap: .5rem;
    --tw-bg-opacity: 1;
    background-color: rgb(236 239 241/var(--tw-bg-opacity));
    padding: 1.5rem 1.25rem 1rem
}

.location-search-modal-header__selected[data-v-1e5da366] {
    margin-top: .5rem
}

.location-search-modal-header__selected p[data-v-1e5da366] {
    font-family: Montserrat,sans-serif;
    font-size: .75rem;
    line-height: 1rem;
    margin-bottom: 0;
    --tw-text-opacity: 1;
    color: rgb(38 50 56/var(--tw-text-opacity))
}

.location-search-modal-header__selected ul[data-v-1e5da366] {
    display: flex;
    flex-wrap: wrap;
    gap: .5rem;
    list-style-type: none;
    margin: 0;
    padding: 0
}

/* Button Component Styles */
a[data-v-dfe864c4][data-v-9266c13a],button[data-v-dfe864c4][data-v-9266c13a] {
    align-items: center;
    border-radius: .25rem;
    border-style: none;
    box-sizing: border-box;
    cursor: pointer;
    display: inline-flex;
    font-family: Montserrat,sans-serif;
    font-weight: 600;
    height: 2.5rem;
    justify-content: center;
    padding: 0 1.5rem;
    transition-duration: .3s
}

.full[data-v-dfe864c4][data-v-9266c13a] {
    width: 100%
}

.big[data-v-dfe864c4][data-v-9266c13a] {
    min-width: 320px
}

.medium[data-v-dfe864c4][data-v-9266c13a] {
    min-width: 152px
}

.small[data-v-dfe864c4][data-v-9266c13a] {
    min-width: 0;
    padding: 0 .5rem
}

/* Outlined Button Styles */
.outlined[data-v-dfe864c4][data-v-9266c13a] {
    background-color: transparent;
    border: 2px solid
}

.outlined--primary[data-v-dfe864c4][data-v-9266c13a] {
    --tw-text-opacity: 1;
    color: rgb(226 83 82/var(--tw-text-opacity))
}

.outlined--primary[data-v-dfe864c4][data-v-9266c13a]:hover {
    background-color: #0000000d;
    --tw-text-opacity: 1;
    color: rgb(208 76 76/var(--tw-text-opacity))
}

.outlined--default[data-v-dfe864c4][data-v-9266c13a] {
    --tw-text-opacity: 1;
    color: rgb(93 122 137/var(--tw-text-opacity))
}

.outlined--default[data-v-dfe864c4][data-v-9266c13a]:hover {
    background-color: #0000000d;
    --tw-text-opacity: 1;
    color: rgb(67 88 98/var(--tw-text-opacity))
}

.outlined--black[data-v-dfe864c4][data-v-9266c13a] {
    --tw-text-opacity: 1;
    color: rgb(38 50 56/var(--tw-text-opacity))
}

.outlined--black[data-v-dfe864c4][data-v-9266c13a]:hover {
    background-color: #0000000d;
    --tw-text-opacity: 1;
    color: rgb(30 40 45/var(--tw-text-opacity))
}

.outlined.disabled[data-v-dfe864c4][data-v-9266c13a] {
    cursor: default;
    --tw-text-opacity: 1;
    color: rgb(207 216 220/var(--tw-text-opacity))
}

.outlined.disabled[data-v-dfe864c4][data-v-9266c13a]:hover {
    background-color: transparent;
    --tw-text-opacity: 1;
    color: rgb(207 216 220/var(--tw-text-opacity))
}

/* Filled Button Styles */
.filled[data-v-dfe864c4][data-v-9266c13a] {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity));
    outline-width: 2px
}

.filled--primary[data-v-dfe864c4][data-v-9266c13a] {
    --tw-bg-opacity: 1;
    background-color: rgb(226 83 82/var(--tw-bg-opacity))
}

.filled--primary[data-v-dfe864c4][data-v-9266c13a]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(208 76 76/var(--tw-bg-opacity))
}

.filled--default[data-v-dfe864c4][data-v-9266c13a] {
    --tw-bg-opacity: 1;
    background-color: rgb(93 122 137/var(--tw-bg-opacity))
}

.filled--default[data-v-dfe864c4][data-v-9266c13a]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(67 88 98/var(--tw-bg-opacity))
}

.filled--black[data-v-dfe864c4][data-v-9266c13a] {
    --tw-bg-opacity: 1;
    background-color: rgb(38 50 56/var(--tw-bg-opacity))
}

.filled--black[data-v-dfe864c4][data-v-9266c13a]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(30 40 45/var(--tw-bg-opacity))
}

.filled.disabled[data-v-dfe864c4][data-v-9266c13a] {
    cursor: default
}

.filled.disabled[data-v-dfe864c4][data-v-9266c13a],.filled.disabled[data-v-dfe864c4][data-v-9266c13a]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(207 216 220/var(--tw-bg-opacity));
    --tw-text-opacity: 1;
    color: rgb(144 164 174/var(--tw-text-opacity))
}

/* Generic component styles without specific data-v attributes for broader compatibility */
.neighborhood-chip {
    align-items: center;
    border-radius: 9999px;
    display: inline-flex;
    gap: .5rem;
    margin-right: .5rem;
    background-color: rgb(38, 50, 56);
    padding: 6px .75rem;
    color: white;
    font-family: Montserrat, sans-serif;
    font-size: .875rem;
    font-weight: 600;
    line-height: 1.25rem;
}

.header-input {
    align-items: center;
    border-radius: 9999px;
    border: 1px solid rgb(202, 212, 216);
    display: flex;
    background-color: white;
    font-family: Montserrat, sans-serif;
    padding-left: .75rem;
    padding-right: .75rem;
}

.header-input--focused {
    border-color: rgb(38, 50, 56);
}

.custom-button {
    align-items: center;
    border-radius: .25rem;
    border: none;
    box-sizing: border-box;
    cursor: pointer;
    display: inline-flex;
    font-family: Montserrat, sans-serif;
    font-weight: 600;
    height: 2.5rem;
    justify-content: center;
    padding: 0 1.5rem;
    transition: all .3s ease;
}

.custom-button--primary {
    background-color: rgb(226, 83, 82);
    color: white;
}

.custom-button--primary:hover {
    background-color: rgb(208, 76, 76);
}

/* Text Size Variants */
.text--sm[data-v-dfe864c4][data-v-9266c13a] {
    font-size: .75rem;
    line-height: 1.5rem
}

.text--md[data-v-dfe864c4][data-v-9266c13a] {
    font-size: .875rem;
    line-height: 1.5rem
}

.text--lg[data-v-dfe864c4][data-v-9266c13a] {
    font-size: 1rem;
    line-height: 1.5rem
}

/* Location Search Modal Actions */
.location-search-modal-actions[data-v-9266c13a] {
    align-items: center;
    border-width: 0;
    display: flex;
    flex-direction: column-reverse;
    gap: .75rem;
    margin-top: auto;
    --tw-border-opacity: 1;
    border-top: 1px rgb(202 212 216/var(--tw-border-opacity));
    border-style: solid;
    padding: 1.25rem
}

@media (min-width: 768px) {
    .location-search-modal-actions[data-v-9266c13a] {
        flex-direction: row
    }
}

/* Neighborhood Alert Component */
.neighborhood-alert[data-v-9e39b931] {
    border-radius: .5rem;
    left: 0;
    margin-left: auto;
    margin-right: auto;
    overflow: hidden;
    position: fixed;
    right: 0;
    top: 1.25rem;
    width: 318px;
    z-index: 50;
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity));
    font-family: Montserrat,sans-serif;
    padding: .75rem;
    --tw-shadow: 0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);
    --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)
}

.neighborhood-alert[data-v-9e39b931]:before {
    border-bottom-left-radius: .5rem;
    border-top-left-radius: .5rem;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 3px;
    --tw-bg-opacity: 1;
    background-color: rgb(237 58 59/var(--tw-bg-opacity));
    --tw-content: none;
    content: var(--tw-content)
}

.neighborhood-alert button[data-v-9e39b931] {
    background-color: transparent;
    border-style: none;
    cursor: pointer
}

.neighborhood-alert header[data-v-9e39b931] {
    align-items: flex-start;
    display: flex;
    gap: .75rem
}

.neighborhood-alert header h2[data-v-9e39b931] {
    flex: 1 1 0%;
    font-size: .875rem;
    font-weight: 600;
    line-height: 1.25rem;
    margin: 0
}

.neighborhood-alert p[data-v-9e39b931] {
    font-size: .75rem;
    line-height: 1rem;
    margin: .25rem 0 0;
    padding: 0 1.75rem
}

/* Search Sponsored Component */
.search-sponrd[data-v-3870ac26] {
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    gap: .5rem;
    padding: .75rem 1.25rem
}

.search-sponrd__image[data-v-3870ac26] {
    height: 320px;
    -o-object-fit: contain;
    object-fit: contain;
    width: 100%
}

@media (min-width: 768px) {
    .search-sponrd__image--mobile[data-v-3870ac26] {
        display: none
    }
}

.search-sponrd__image--desktop[data-v-3870ac26] {
    display: none;
    height: 250px
}

@media (min-width: 768px) {
    .search-sponrd__image--desktop[data-v-3870ac26] {
        display: block
    }
}

/* Location Search Modal Content */
.location-search-modal-content[data-v-eb92632c] {
    flex: 1 1 auto;
    height: auto;
    list-style-type: none;
    overflow-y: auto;
    padding: .5rem 1.25rem
}

@media (min-width: 768px) {
    .location-search-modal-content[data-v-eb92632c] {
        flex: 0 1 auto
    }
}

.location-search-modal-content p[data-v-eb92632c] {
    font-family: Montserrat,sans-serif;
    font-size: .875rem;
    font-weight: 500;
    line-height: 1.25rem;
    margin: 0;
    --tw-text-opacity: 1;
    color: rgb(38 50 56/var(--tw-text-opacity))
}

.location-search-modal-content__location[data-v-eb92632c] {
    align-items: center;
    border-width: 0;
    cursor: pointer;
    display: flex;
    gap: .75rem;
    --tw-border-opacity: 1;
    border-bottom: 1px rgb(202 212 216/var(--tw-border-opacity));
    border-style: solid;
    padding-bottom: 17px;
    padding-top: 17px
}

.location-search-modal-content__location--border[data-v-eb92632c] {
    border-width: 0;
    --tw-border-opacity: 1;
    border-bottom: 1px rgb(202 212 216/var(--tw-border-opacity));
    border-style: solid;
    padding: 1rem 1.25rem
}

.location-search-modal-content__shots[data-v-eb92632c] {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 5px!important;
    height: 52px;
    margin-top: .5rem
}

.location-search-modal-content__shots--gif[data-v-eb92632c] {
    height: 1.75rem;
    position: relative;
    width: 1.75rem
}

.location-search-modal-content__shots--gif>img[data-v-eb92632c] {
    left: 50%;
    position: absolute;
    top: 50%;
    transform: translate(-65%,-60%)
}

.location-search-modal-content__shots>p[data-v-eb92632c] {
    flex: 1 1 0%;
    font-size: 1rem;
    line-height: 1.25rem;
    --tw-text-opacity: 1;
    color: rgb(226 83 82/var(--tw-text-opacity))
}

.location-search-modal-content ul[data-v-eb92632c] {
    font-family: Montserrat,sans-serif;
    padding: 0
}

.location-search-modal-content ul li[data-v-eb92632c] {
    align-items: center;
    cursor: pointer;
    display: flex;
    font-weight: 500;
    gap: .75rem;
    margin-top: .5rem;
    padding-bottom: 1rem;
    padding-top: 1rem
}

.location-search-modal-content ul li p[data-v-eb92632c] {
    flex: 1 1 0%
}

.location-search-modal-content ul li.not-allowed[data-v-eb92632c] {
    cursor: not-allowed
}

.location-search-modal-content ul li.location-search-modal-content__location--checkbox[data-v-eb92632c] {
    display: grid;
    grid-template-columns: 24px 1fr
}

.location-search-modal-content ul input[type=checkbox][data-v-eb92632c] {
    accent-color: #263238;
    height: 18px;
    width: 18px
}

@media (width <= 767px) {
    .location-search-modal-content--main-cities[data-v-eb92632c] {
        min-height: 311px
    }

    .location-search-modal-content--searched-cities[data-v-eb92632c] {
        height: calc(100% - 440px)
    }
}

/* Geolocation Overlay Component */
.geolocation-overlay[data-v-29137494] {
    align-items: center;
    background-color: #01071f99;
    display: flex;
    height: 100%;
    justify-content: center;
    position: fixed;
    width: 100%
}

.geolocation-wrapper[data-v-29137494] {
    border-radius: .5rem;
    position: relative;
    width: 320px;
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity));
    font-family: Montserrat,sans-serif;
    padding: 1rem 1.25rem;
    --tw-text-opacity: 1;
    color: rgb(38 50 56/var(--tw-text-opacity))
}

.geolocation-wrapper__header[data-v-29137494],.geolocation-wrapper__video-header[data-v-29137494] {
    align-items: center;
    display: flex;
    font-size: 1rem;
    gap: .75rem;
    line-height: 1.25rem;
    --tw-text-opacity: 1;
    color: rgb(38 50 56/var(--tw-text-opacity))
}

.geolocation-wrapper__header h2[data-v-29137494] {
    display: flex;
    flex-direction: column;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.25rem;
    margin: 0
}

.geolocation-wrapper__video-header[data-v-29137494] {
    padding: 0 1.25rem 1rem
}

.geolocation-wrapper__video-header h2[data-v-29137494] {
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.25rem;
    margin: 0
}

.geolocation-wrapper__video-header svg[data-v-29137494] {
    cursor: pointer
}

.geolocation-wrapper__close[data-v-29137494] {
    position: absolute;
    right: 1.25rem;
    top: 1rem
}

.geolocation-wrapper .unstyled-button[data-v-29137494] {
    background-color: transparent;
    border-style: none;
    cursor: pointer;
    height: 1.5rem;
    width: 1.5rem
}

.geolocation-wrapper__video-content[data-v-29137494] {
    border-radius: .25rem .25rem .5rem .5rem;
    height: 344px;
    overflow: hidden
}

.geolocation-wrapper__content--separator[data-v-29137494] {
    height: 1px;
    margin: .5rem 0;
    width: 100%;
    --tw-bg-opacity: 1;
    background-color: rgb(198 201 202/var(--tw-bg-opacity))
}

.geolocation-wrapper__content h2[data-v-29137494] {
    font-size: .875rem;
    font-weight: 600;
    line-height: 1.25rem;
    margin: 0;
    padding-bottom: .5rem
}

.geolocation-wrapper__content p[data-v-29137494] {
    font-size: .875rem;
    line-height: 1.25rem;
    margin: 0
}

.geolocation-wrapper__content a[data-v-29137494],.geolocation-wrapper__content button[data-v-29137494] {
    background-color: transparent;
    border-style: none;
    cursor: pointer;
    display: block;
    font-size: .75rem;
    line-height: 1rem;
    margin-top: .75rem;
    --tw-text-opacity: 1;
    color: rgb(93 122 137/var(--tw-text-opacity));
    text-decoration-line: underline
}

.geolocation-wrapper__content footer[data-v-29137494] {
    margin-top: .75rem
}

.geolocation-wrapper__content footer a[data-v-29137494] {
    align-items: center;
    display: inline-flex;
    gap: .25rem;
    margin: 0
}

.geolocation-wrapper__content footer svg[data-v-29137494] {
    align-items: center;
    display: flex;
    justify-content: center
}

.geolocation-wrapper--show-video[data-v-29137494] {
    padding: 1rem 0 0
}

/* Additional Button Styles with Different Data Attributes */
a[data-v-dfe864c4][data-v-ecff6c9a],button[data-v-dfe864c4][data-v-ecff6c9a] {
    align-items: center;
    border-radius: .25rem;
    border-style: none;
    box-sizing: border-box;
    cursor: pointer;
    display: inline-flex;
    font-family: Montserrat,sans-serif;
    font-weight: 600;
    height: 2.5rem;
    justify-content: center;
    padding: 0 1.5rem;
    transition-duration: .3s
}

.full[data-v-dfe864c4][data-v-ecff6c9a] {
    width: 100%
}

.big[data-v-dfe864c4][data-v-ecff6c9a] {
    min-width: 320px
}

.medium[data-v-dfe864c4][data-v-ecff6c9a] {
    min-width: 152px
}

.small[data-v-dfe864c4][data-v-ecff6c9a] {
    min-width: 0;
    padding: 0 .5rem
}

.outlined[data-v-dfe864c4][data-v-ecff6c9a] {
    background-color: transparent;
    border: 2px solid
}

.outlined--primary[data-v-dfe864c4][data-v-ecff6c9a] {
    --tw-text-opacity: 1;
    color: rgb(226 83 82/var(--tw-text-opacity))
}

.outlined--primary[data-v-dfe864c4][data-v-ecff6c9a]:hover {
    background-color: #0000000d;
    --tw-text-opacity: 1;
    color: rgb(208 76 76/var(--tw-text-opacity))
}

.outlined--default[data-v-dfe864c4][data-v-ecff6c9a] {
    --tw-text-opacity: 1;
    color: rgb(93 122 137/var(--tw-text-opacity))
}

.outlined--default[data-v-dfe864c4][data-v-ecff6c9a]:hover {
    background-color: #0000000d;
    --tw-text-opacity: 1;
    color: rgb(67 88 98/var(--tw-text-opacity))
}

.outlined--black[data-v-dfe864c4][data-v-ecff6c9a] {
    --tw-text-opacity: 1;
    color: rgb(38 50 56/var(--tw-text-opacity))
}

.outlined--black[data-v-dfe864c4][data-v-ecff6c9a]:hover {
    background-color: #0000000d;
    --tw-text-opacity: 1;
    color: rgb(30 40 45/var(--tw-text-opacity))
}

.outlined.disabled[data-v-dfe864c4][data-v-ecff6c9a] {
    cursor: default;
    --tw-text-opacity: 1;
    color: rgb(207 216 220/var(--tw-text-opacity))
}

.outlined.disabled[data-v-dfe864c4][data-v-ecff6c9a]:hover {
    background-color: transparent;
    --tw-text-opacity: 1;
    color: rgb(207 216 220/var(--tw-text-opacity))
}

.filled[data-v-dfe864c4][data-v-ecff6c9a] {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity));
    outline-width: 2px
}

.filled--primary[data-v-dfe864c4][data-v-ecff6c9a] {
    --tw-bg-opacity: 1;
    background-color: rgb(226 83 82/var(--tw-bg-opacity))
}

.filled--primary[data-v-dfe864c4][data-v-ecff6c9a]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(208 76 76/var(--tw-bg-opacity))
}

.filled--default[data-v-dfe864c4][data-v-ecff6c9a] {
    --tw-bg-opacity: 1;
    background-color: rgb(93 122 137/var(--tw-bg-opacity))
}

.filled--default[data-v-dfe864c4][data-v-ecff6c9a]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(67 88 98/var(--tw-bg-opacity))
}

.filled--black[data-v-dfe864c4][data-v-ecff6c9a] {
    --tw-bg-opacity: 1;
    background-color: rgb(38 50 56/var(--tw-bg-opacity))
}

.filled--black[data-v-dfe864c4][data-v-ecff6c9a]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(30 40 45/var(--tw-bg-opacity))
}

.filled.disabled[data-v-dfe864c4][data-v-ecff6c9a] {
    cursor: default
}

.filled.disabled[data-v-dfe864c4][data-v-ecff6c9a],.filled.disabled[data-v-dfe864c4][data-v-ecff6c9a]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(207 216 220/var(--tw-bg-opacity));
    --tw-text-opacity: 1;
    color: rgb(144 164 174/var(--tw-text-opacity))
}

.text--sm[data-v-dfe864c4][data-v-ecff6c9a] {
    font-size: .75rem;
    line-height: 1.5rem
}

.text--md[data-v-dfe864c4][data-v-ecff6c9a] {
    font-size: .875rem;
    line-height: 1.5rem
}

.text--lg[data-v-dfe864c4][data-v-ecff6c9a] {
    font-size: 1rem;
    line-height: 1.5rem
}

/* Location Search Modal */
.location-search-modal[data-v-ecff6c9a] {
    left: 0;
    top: 0;
    z-index: 40
}

.location-search-modal[data-v-ecff6c9a],.location-search-modal__overlay[data-v-ecff6c9a] {
    align-items: center;
    display: flex;
    height: 100%;
    justify-content: center;
    position: fixed;
    width: 100%
}

.location-search-modal__overlay[data-v-ecff6c9a] {
    background-color: #01071f99;
    padding-bottom: 1rem;
    padding-top: 1rem
}

.location-search-modal__wrapper[data-v-ecff6c9a] {
    border-radius: .5rem;
    display: flex;
    flex-direction: column;
    height: 100dvh;
    overflow: auto;
    width: 100%;
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

@media (min-width: 768px) {
    .location-search-modal__wrapper[data-v-ecff6c9a] {
        width: 700px
    }

    .location-search-modal__wrapper--no-banner[data-v-ecff6c9a] {
        height: 500px
    }

    .location-search-modal__wrapper--with-banner[data-v-ecff6c9a] {
        height: 100%;
        max-height: 768px
    }
}

/* Search Cities Component */
.search-cities--fixed[data-v-0dc2188f] {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
    position: fixed
}

@media (min-width: 1280px) {
    .search-cities--fixed .search-cities[data-v-0dc2188f] {
        justify-content: center
    }
}

.search-cities--spacing-to-top[data-v-0dc2188f] {
    top: 5rem
}

/* Generic fallback styles without data-v attributes */
.location-search-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 40;
    display: flex;
    align-items: center;
    justify-content: center;
}

.location-search-modal__overlay {
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: rgba(1, 7, 31, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.location-search-modal__wrapper {
    background-color: white;
    border-radius: .5rem;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100vh;
    overflow: auto;
}

@media (min-width: 768px) {
    .location-search-modal__wrapper {
        width: 700px;
        height: auto;
        max-height: 768px;
    }
}

.geolocation-overlay {
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: rgba(1, 7, 31, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
}

.geolocation-wrapper {
    background-color: white;
    border-radius: .5rem;
    padding: 1rem 1.25rem;
    width: 320px;
    position: relative;
    font-family: Montserrat, sans-serif;
    color: rgb(38, 50, 56);
}

.neighborhood-alert {
    position: fixed;
    top: 1.25rem;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 318px;
    background-color: white;
    border-radius: .5rem;
    padding: .75rem;
    font-family: Montserrat, sans-serif;
    box-shadow: 0 4px 6px -1px rgba(0,0,0,.1), 0 2px 4px -2px rgba(0,0,0,.1);
    z-index: 50;
    overflow: hidden;
}

.neighborhood-alert:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background-color: rgb(237, 58, 59);
    border-top-left-radius: .5rem;
    border-bottom-left-radius: .5rem;
}
