/* Custom Component Styles - Additional CSS for Vue Components */

/* Neighborhood Chip Component */
.neighborhood-chip[data-v-69f6020a] {
    align-items: center;
    border-radius: 9999px;
    display: inline-flex;
    gap: .5rem;
    margin-right: .5rem;
    --tw-bg-opacity: 1;
    background-color: rgb(38 50 56/var(--tw-bg-opacity));
    padding: 6px .75rem
}

.neighborhood-chip p[data-v-69f6020a] {
    font-family: Montserrat,sans-serif;
    font-size: .875rem;
    font-weight: 600;
    line-height: 1.25rem;
    margin: 0;
    padding: 0;
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

.neighborhood-chip__close[data-v-69f6020a] {
    align-items: center;
    border-radius: 9999px;
    cursor: pointer;
    display: flex;
    height: 1rem;
    justify-content: center;
    width: 1rem;
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity))
}

/* Header Input Component */
.icon-pointer[data-v-69dca9ee] {
    cursor: pointer
}

.header-input[data-v-69dca9ee] {
    align-items: center;
    border-radius: 9999px;
    border-style: solid;
    border-width: 1px;
    display: flex;
    --tw-border-opacity: 1;
    border-color: rgb(202 212 216/var(--tw-border-opacity));
    --tw-bg-opacity: 1;
    background-color: rgb(255 255 255/var(--tw-bg-opacity));
    font-family: Montserrat,sans-serif;
    padding-left: .75rem;
    padding-right: .75rem
}

.header-input--focused[data-v-69dca9ee] {
    --tw-border-opacity: 1;
    border-color: rgb(38 50 56/var(--tw-border-opacity))
}

.header-input input[data-v-69dca9ee] {
    background-color: transparent;
    border-style: none;
    flex: 1 1 0%;
    font-size: .875rem;
    height: 100%;
    line-height: 1.25rem;
    padding: 14px .75rem;
    --tw-text-opacity: 1;
    color: rgb(38 50 56/var(--tw-text-opacity));
    outline: 2px solid transparent;
    outline-offset: 2px
}

.header-input input[data-v-69dca9ee]::-moz-placeholder {
    --tw-text-opacity: 1;
    color: rgb(127 138 148/var(--tw-text-opacity))
}

.header-input input[data-v-69dca9ee]::placeholder {
    --tw-text-opacity: 1;
    color: rgb(127 138 148/var(--tw-text-opacity))
}

.header-input__counter[data-v-69dca9ee] {
    align-items: center;
    border-radius: 1.5rem;
    display: flex;
    justify-content: center;
    --tw-bg-opacity: 1;
    background-color: rgb(38 50 56/var(--tw-bg-opacity));
    font-family: Montserrat,sans-serif;
    font-size: .875rem;
    font-weight: 600;
    line-height: 1.25rem;
    padding: 6px .75rem;
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity))
}

/* Location Search Modal Component */
.location-search-modal-header[data-v-1e5da366] {
    display: flex;
    flex-direction: column;
    gap: .5rem;
    --tw-bg-opacity: 1;
    background-color: rgb(236 239 241/var(--tw-bg-opacity));
    padding: 1.5rem 1.25rem 1rem
}

.location-search-modal-header__selected[data-v-1e5da366] {
    margin-top: .5rem
}

.location-search-modal-header__selected p[data-v-1e5da366] {
    font-family: Montserrat,sans-serif;
    font-size: .75rem;
    line-height: 1rem;
    margin-bottom: 0;
    --tw-text-opacity: 1;
    color: rgb(38 50 56/var(--tw-text-opacity))
}

.location-search-modal-header__selected ul[data-v-1e5da366] {
    display: flex;
    flex-wrap: wrap;
    gap: .5rem;
    list-style-type: none;
    margin: 0;
    padding: 0
}

/* Button Component Styles */
a[data-v-dfe864c4][data-v-9266c13a],button[data-v-dfe864c4][data-v-9266c13a] {
    align-items: center;
    border-radius: .25rem;
    border-style: none;
    box-sizing: border-box;
    cursor: pointer;
    display: inline-flex;
    font-family: Montserrat,sans-serif;
    font-weight: 600;
    height: 2.5rem;
    justify-content: center;
    padding: 0 1.5rem;
    transition-duration: .3s
}

.full[data-v-dfe864c4][data-v-9266c13a] {
    width: 100%
}

.big[data-v-dfe864c4][data-v-9266c13a] {
    min-width: 320px
}

.medium[data-v-dfe864c4][data-v-9266c13a] {
    min-width: 152px
}

.small[data-v-dfe864c4][data-v-9266c13a] {
    min-width: 0;
    padding: 0 .5rem
}

/* Outlined Button Styles */
.outlined[data-v-dfe864c4][data-v-9266c13a] {
    background-color: transparent;
    border: 2px solid
}

.outlined--primary[data-v-dfe864c4][data-v-9266c13a] {
    --tw-text-opacity: 1;
    color: rgb(226 83 82/var(--tw-text-opacity))
}

.outlined--primary[data-v-dfe864c4][data-v-9266c13a]:hover {
    background-color: #0000000d;
    --tw-text-opacity: 1;
    color: rgb(208 76 76/var(--tw-text-opacity))
}

.outlined--default[data-v-dfe864c4][data-v-9266c13a] {
    --tw-text-opacity: 1;
    color: rgb(93 122 137/var(--tw-text-opacity))
}

.outlined--default[data-v-dfe864c4][data-v-9266c13a]:hover {
    background-color: #0000000d;
    --tw-text-opacity: 1;
    color: rgb(67 88 98/var(--tw-text-opacity))
}

.outlined--black[data-v-dfe864c4][data-v-9266c13a] {
    --tw-text-opacity: 1;
    color: rgb(38 50 56/var(--tw-text-opacity))
}

.outlined--black[data-v-dfe864c4][data-v-9266c13a]:hover {
    background-color: #0000000d;
    --tw-text-opacity: 1;
    color: rgb(30 40 45/var(--tw-text-opacity))
}

.outlined.disabled[data-v-dfe864c4][data-v-9266c13a] {
    cursor: default;
    --tw-text-opacity: 1;
    color: rgb(207 216 220/var(--tw-text-opacity))
}

.outlined.disabled[data-v-dfe864c4][data-v-9266c13a]:hover {
    background-color: transparent;
    --tw-text-opacity: 1;
    color: rgb(207 216 220/var(--tw-text-opacity))
}

/* Filled Button Styles */
.filled[data-v-dfe864c4][data-v-9266c13a] {
    --tw-text-opacity: 1;
    color: rgb(255 255 255/var(--tw-text-opacity));
    outline-width: 2px
}

.filled--primary[data-v-dfe864c4][data-v-9266c13a] {
    --tw-bg-opacity: 1;
    background-color: rgb(226 83 82/var(--tw-bg-opacity))
}

.filled--primary[data-v-dfe864c4][data-v-9266c13a]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(208 76 76/var(--tw-bg-opacity))
}

.filled--default[data-v-dfe864c4][data-v-9266c13a] {
    --tw-bg-opacity: 1;
    background-color: rgb(93 122 137/var(--tw-bg-opacity))
}

.filled--default[data-v-dfe864c4][data-v-9266c13a]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(67 88 98/var(--tw-bg-opacity))
}

.filled--black[data-v-dfe864c4][data-v-9266c13a] {
    --tw-bg-opacity: 1;
    background-color: rgb(38 50 56/var(--tw-bg-opacity))
}

.filled--black[data-v-dfe864c4][data-v-9266c13a]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(30 40 45/var(--tw-bg-opacity))
}

.filled.disabled[data-v-dfe864c4][data-v-9266c13a] {
    cursor: default
}

.filled.disabled[data-v-dfe864c4][data-v-9266c13a],.filled.disabled[data-v-dfe864c4][data-v-9266c13a]:hover {
    --tw-bg-opacity: 1;
    background-color: rgb(207 216 220/var(--tw-bg-opacity));
    --tw-text-opacity: 1;
    color: rgb(144 164 174/var(--tw-text-opacity))
}

/* Generic component styles without specific data-v attributes for broader compatibility */
.neighborhood-chip {
    align-items: center;
    border-radius: 9999px;
    display: inline-flex;
    gap: .5rem;
    margin-right: .5rem;
    background-color: rgb(38, 50, 56);
    padding: 6px .75rem;
    color: white;
    font-family: Montserrat, sans-serif;
    font-size: .875rem;
    font-weight: 600;
    line-height: 1.25rem;
}

.header-input {
    align-items: center;
    border-radius: 9999px;
    border: 1px solid rgb(202, 212, 216);
    display: flex;
    background-color: white;
    font-family: Montserrat, sans-serif;
    padding-left: .75rem;
    padding-right: .75rem;
}

.header-input--focused {
    border-color: rgb(38, 50, 56);
}

.custom-button {
    align-items: center;
    border-radius: .25rem;
    border: none;
    box-sizing: border-box;
    cursor: pointer;
    display: inline-flex;
    font-family: Montserrat, sans-serif;
    font-weight: 600;
    height: 2.5rem;
    justify-content: center;
    padding: 0 1.5rem;
    transition: all .3s ease;
}

.custom-button--primary {
    background-color: rgb(226, 83, 82);
    color: white;
}

.custom-button--primary:hover {
    background-color: rgb(208, 76, 76);
}
