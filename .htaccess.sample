<IfModule mod_rewrite.c>
# That was ONLY to protect you from 500 errors
# if your server did not have mod_rewrite enabled

RewriteEngine On
# RewriteBase /
# NOT needed unless you're using mod_alias to redirect

# Direct all requests to /public folder
RewriteCond %{REQUEST_URI} !/public
RewriteRule ^(.*)$ public/$1 [L]

# Direct all requests to pretty url
RewriteCond %{THE_REQUEST} ^.*/index\.php
RewriteRule ^(.*)index.php$ /$1 [R=301,L]

# Disable Directory listing
Options -Indexes

# block files which needs to be hidden, specify .example extension of the file
<Files ~ "\.(env|config.js|md|gitignore|gitattributes|lock)$">
    Order allow,deny
    Deny from all
</Files>

</IfModule>
