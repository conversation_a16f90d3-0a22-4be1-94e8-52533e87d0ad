/*!
*
* Laravel Big bang default theme with light - dark alternatives
*
 */

@import "../../node_modules/bootstrap/scss/functions";
@import "./bootstrap/dark/functions";

@import "./variables";  // Base theme variables
@import "../../node_modules/bootstrap/scss/variables";
//@import "./variables";  // Base theme variables
@import "./variables-dark";  // Dark theme colors override | only the dark colors mapped as `-alt`
$color-scheme-alt: light;
$isRTL : false;

@import "../../node_modules/bootstrap/scss/mixins";
@import "./bootstrap/dark/mixins";

@import "./bootstrap/dark/utilities/color-schemes";
@import "./bootstrap/typography";
@import "./bootstrap/gradients";

@import "../../node_modules/bootstrap/scss/root";
@import "../../node_modules/bootstrap/scss/reboot";
@import "../../node_modules/bootstrap/scss/type";
@import "../../node_modules/bootstrap/scss/images";
@import "../../node_modules/bootstrap/scss/code";
@import "../../node_modules/bootstrap/scss/grid";
@import "../../node_modules/bootstrap/scss/tables";
@import "../../node_modules/bootstrap/scss/forms";
@import "../../node_modules/bootstrap/scss/buttons";
@import "./bootstrap/buttons";
@import "../../node_modules/bootstrap/scss/transitions";
@import "./bootstrap/dropdown";
@import "../../node_modules/bootstrap/scss/dropdown";
@import "../../node_modules/bootstrap/scss/button-group";
@import "../../node_modules/bootstrap/scss/input-group";
@import "../../node_modules/bootstrap/scss/custom-forms";
@import "../../node_modules/bootstrap/scss/nav";
@import "../../node_modules/bootstrap/scss/navbar";
@import "../../node_modules/bootstrap/scss/card";
@import "../../node_modules/bootstrap/scss/breadcrumb";
@import "../../node_modules/bootstrap/scss/pagination";
@import "../../node_modules/bootstrap/scss/badge";
@import "./bootstrap/badge";
@import "../../node_modules/bootstrap/scss/alert";
@import "./bootstrap/alert";
@import "../../node_modules/bootstrap/scss/progress";
@import "../../node_modules/bootstrap/scss/media";
@import "../../node_modules/bootstrap/scss/list-group";
@import "../../node_modules/bootstrap/scss/close";
@import "../../node_modules/bootstrap/scss/toasts";
@import "../../node_modules/bootstrap/scss/modal";
@import "../../node_modules/bootstrap/scss/tooltip";
@import "../../node_modules/bootstrap/scss/popover";
@import "../../node_modules/bootstrap/scss/carousel";
@import "../../node_modules/bootstrap/scss/spinners";
@import "../../node_modules/bootstrap/scss/utilities";
@import "./bootstrap/utilities";
@import "../../node_modules/bootstrap/scss/print";

    @import "./bootstrap/dark/root";
    @import "./bootstrap/dark/reboot";
    @import "./bootstrap/dark/type";
    @import "./bootstrap/dark/images";
    @import "./bootstrap/dark/code";
    @import "./bootstrap/dark/tables";
    @import "./bootstrap/dark/forms";
    @import "./bootstrap/dark/buttons";
    // _transitions not used
    @import "./bootstrap/dark/dropdown";
    @import "./bootstrap/dark/button-group";
    @import "./bootstrap/dark/input-group";
    @import "./bootstrap/dark/custom-forms";
    @import "./bootstrap/dark/nav";
    @import "./bootstrap/dark/navbar";
    @import "./bootstrap/dark/card";
    @import "./bootstrap/dark/breadcrumb";
    @import "./bootstrap/dark/pagination";
    @import "./bootstrap/dark/badge";
    @import "./bootstrap/dark/alert";
    @import "./bootstrap/dark/progress";
    // _media not used
    @import "./bootstrap/dark/list-group";
    @import "./bootstrap/dark/close";
    @import "./bootstrap/dark/toasts";
    @import "./bootstrap/dark/modal";
    @import "./bootstrap/dark/tooltip";
    @import "./bootstrap/dark/popover";
    @import "./bootstrap/dark/carousel";
    // _spinners not used
    @import "./bootstrap/dark/utilities";
    @import "./bootstrap/dark/dark";

@import "./bootstrap/dark/utilities/display-dark";

// Preloading Elements
$ph-bg:                   lighten($gray-900-alt,8%) !default;
$ph-color:                darken($gray-800-alt,8%) !default;
@import "../../node_modules/placeholder-loading/src/scss/placeholder-loading";

footer .copyRightInfo .nav-link:nth-last-child(1){
    padding-right:0px!important;
}

footer .footer-social-links a:nth-last-child(1){
    margin-right:0px!important;
}

@import "./generic";
