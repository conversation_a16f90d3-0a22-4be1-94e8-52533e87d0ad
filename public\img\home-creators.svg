<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 774.7 548.13"><defs><linearGradient id="linear-gradient" x1="778" y1="-2608.88" x2="858.29" y2="-2409.27" gradientTransform="translate(0 3556.5)" gradientUnits="userSpaceOnUse"><stop offset="0.61" stop-color="#e0e0e0"/><stop offset="0.67" stop-color="#dadada"/><stop offset="0.75" stop-color="#c9c9c9"/><stop offset="0.84" stop-color="#acacac"/><stop offset="0.85" stop-color="#a8a8a8"/></linearGradient><clipPath id="clip-path" transform="translate(-349 -177.27)"><polygon points="908.6 497.9 304.1 757.1 150.2 398.3 367.7 305.1 553.3 482.8 470.6 261 754.7 139.1 908.6 497.9" fill="none"/></clipPath><clipPath id="clip-path-2" transform="translate(-349 -177.27)"><rect x="730.36" y="323.87" width="152.2" height="657.69" transform="translate(-164.47 1018.37) rotate(-59.62)" fill="none"/></clipPath><radialGradient id="radial-gradient" cx="439.42" cy="-2558.26" r="668.29" gradientTransform="translate(0 3204)" gradientUnits="userSpaceOnUse"><stop offset="0.61" stop-color="#e0e0e0"/><stop offset="0.67" stop-color="#dadada"/><stop offset="0.75" stop-color="#c9c9c9"/><stop offset="0.84" stop-color="#acacac"/></radialGradient><linearGradient id="linear-gradient-2" x1="446" y1="-585.05" x2="918" y2="-585.05" gradientTransform="matrix(1, 0, 0, -1, 0, -80)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-3" x1="531.25" y1="-568.5" x2="494.16" y2="-499.13" gradientTransform="matrix(1, 0, 0, -1, 0, -80)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-4" x1="531.29" y1="-584.07" x2="494.2" y2="-514.7" gradientTransform="matrix(1, 0, 0, -1, 0, -80)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-5" x1="531.29" y1="-599.66" x2="494.2" y2="-530.29" gradientTransform="matrix(1, 0, 0, -1, 0, -80)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-6" x1="531.28" y1="-615.26" x2="494.19" y2="-545.89" gradientTransform="matrix(1, 0, 0, -1, 0, -80)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-7" x1="531.28" y1="-630.85" x2="494.19" y2="-561.48" gradientTransform="matrix(1, 0, 0, -1, 0, -80)" xlink:href="#linear-gradient"/><linearGradient id="linear-gradient-8" x1="531.27" y1="-646.44" x2="494.19" y2="-577.07" gradientTransform="matrix(1, 0, 0, -1, 0, -80)" xlink:href="#linear-gradient"/><clipPath id="clip-path-3" transform="translate(-349 -177.27)"><path d="M820.7,515.2a11.39,11.39,0,0,1-11.4-11.4V433a11.39,11.39,0,0,1,11.4-11.4H893A11.39,11.39,0,0,1,904.4,433v70.9A11.39,11.39,0,0,1,893,515.3H820.7Z" fill="none"/></clipPath><clipPath id="clip-path-4" transform="translate(-349 -177.27)"><rect x="809.3" y="533" width="95.1" height="93.7" rx="11.4" fill="none"/></clipPath><linearGradient id="linear-gradient-9" x1="780.99" y1="-611.1" x2="837.71" y2="-611.1" gradientTransform="matrix(1, 0, 0, -1, -349, -257.27)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#cb0c9f"/><stop offset="1" stop-color="#921ba6"/></linearGradient><linearGradient id="linear-gradient-10" x1="555.03" y1="562.5" x2="1002.03" y2="901.5" gradientTransform="matrix(1, 0, 0, -1, 0, 1026)" xlink:href="#linear-gradient-9"/><linearGradient id="linear-gradient-11" x1="1134.22" y1="-2674.6" x2="1135.11" y2="-2674.6" gradientTransform="matrix(42.72, 0, 0, -36.01, -47787.55, -96130.26)" xlink:href="#linear-gradient-9"/><linearGradient id="linear-gradient-12" x1="740.91" y1="688.03" x2="736.91" y2="618.03" gradientTransform="matrix(1, 0, 0, -1, 0, 1026)" xlink:href="#linear-gradient-9"/><linearGradient id="linear-gradient-13" x1="718.03" y1="689.34" x2="714.03" y2="619.34" gradientTransform="matrix(1, 0, 0, -1, 0, 1026)" xlink:href="#linear-gradient-9"/></defs><title>home-creators</title><path d="M436,706.1h0c4.5-2.4,10.7-4.3,14.1-4.3H914.5c3.4,0,9.6,1.9,14.1,4.3h0c4.6,2.5,5.6,4.6,2,4.6H434.1C430.4,710.7,431.4,708.6,436,706.1Z" transform="translate(-349 -177.27)" fill="url(#linear-gradient)"/><g clip-path="url(#clip-path)"><circle cx="212.2" cy="335.93" r="212.2" fill="#fafafa"/></g><g clip-path="url(#clip-path-2)"><circle cx="562.5" cy="303.43" r="212.2" fill="#a4afda"/></g><path d="M901.1,652H462.9a31.5,31.5,0,0,1-31.5-31.5V389.6a31.5,31.5,0,0,1,31.5-31.5H901.1a31.5,31.5,0,0,1,31.5,31.5V620.5A31.43,31.43,0,0,1,901.1,652Z" transform="translate(-349 -177.27)" fill="#f0f0f0"/><path d="M462.9,633.9a13.4,13.4,0,0,1-13.4-13.4V389.6a13.4,13.4,0,0,1,13.4-13.4H901.1a13.4,13.4,0,0,1,13.4,13.4V620.5a13.4,13.4,0,0,1-13.4,13.4Z" transform="translate(-349 -177.27)" fill="url(#radial-gradient)"/><path d="M901.1,376.2a13.4,13.4,0,0,1,13.4,13.4V620.5a13.4,13.4,0,0,1-13.4,13.4H462.9a13.4,13.4,0,0,1-13.4-13.4V389.6a13.4,13.4,0,0,1,13.4-13.4H901.1m0-3.5H462.9A16.94,16.94,0,0,0,446,389.6V620.5a16.94,16.94,0,0,0,16.9,16.9H901.1A16.94,16.94,0,0,0,918,620.5V389.6a16.82,16.82,0,0,0-16.9-16.9Z" transform="translate(-349 -177.27)" fill="url(#linear-gradient-2)"/><path d="M794.3,436.4V633.9H561.5V436.4a13.29,13.29,0,0,1,13.3-13.3H781A13.29,13.29,0,0,1,794.3,436.4Z" transform="translate(-349 -177.27)" fill="#fff"/><path d="M470.6,431.5a7.81,7.81,0,0,1,7.8-7.8h60.1a7.81,7.81,0,0,1,7.8,7.8v121a7.81,7.81,0,0,1-7.8,7.8H478.4a7.81,7.81,0,0,1-7.8-7.8Z" transform="translate(-349 -177.27)" fill="#fff"/><path d="M527,447.4H487.6a3.76,3.76,0,0,1-3.7-3.7h0a3.76,3.76,0,0,1,3.7-3.7H527a3.76,3.76,0,0,1,3.7,3.7h0A3.69,3.69,0,0,1,527,447.4Z" transform="translate(-349 -177.27)" fill="url(#linear-gradient-3)"/><path d="M527,462.9H487.6a3.76,3.76,0,0,1-3.7-3.7h0a3.76,3.76,0,0,1,3.7-3.7H527a3.76,3.76,0,0,1,3.7,3.7h0A3.63,3.63,0,0,1,527,462.9Z" transform="translate(-349 -177.27)" fill="url(#linear-gradient-4)"/><path d="M527,478.5H487.6a3.76,3.76,0,0,1-3.7-3.7h0a3.76,3.76,0,0,1,3.7-3.7H527a3.76,3.76,0,0,1,3.7,3.7h0A3.63,3.63,0,0,1,527,478.5Z" transform="translate(-349 -177.27)" fill="url(#linear-gradient-5)"/><path d="M527,494.1H487.6a3.76,3.76,0,0,1-3.7-3.7h0a3.76,3.76,0,0,1,3.7-3.7H527a3.76,3.76,0,0,1,3.7,3.7h0A3.63,3.63,0,0,1,527,494.1Z" transform="translate(-349 -177.27)" fill="url(#linear-gradient-6)"/><path d="M527,509.7H487.6a3.76,3.76,0,0,1-3.7-3.7h0a3.76,3.76,0,0,1,3.7-3.7H527a3.76,3.76,0,0,1,3.7,3.7h0A3.63,3.63,0,0,1,527,509.7Z" transform="translate(-349 -177.27)" fill="url(#linear-gradient-7)"/><path d="M527,525.3H487.6a3.76,3.76,0,0,1-3.7-3.7h0a3.76,3.76,0,0,1,3.7-3.7H527a3.76,3.76,0,0,1,3.7,3.7h0A3.63,3.63,0,0,1,527,525.3Z" transform="translate(-349 -177.27)" fill="url(#linear-gradient-8)"/><path d="M820.7,515.2a11.39,11.39,0,0,1-11.4-11.4V433a11.39,11.39,0,0,1,11.4-11.4H893A11.39,11.39,0,0,1,904.4,433v70.9A11.39,11.39,0,0,1,893,515.3H820.7Z" transform="translate(-349 -177.27)" fill="#fff"/><g clip-path="url(#clip-path-3)"><circle cx="460.3" cy="242.33" r="28.4" fill="#a4afda"/></g><path d="M820.7,626.7a11.39,11.39,0,0,1-11.4-11.4V544.4A11.39,11.39,0,0,1,820.7,533H893a11.39,11.39,0,0,1,11.4,11.4v70.9A11.39,11.39,0,0,1,893,626.7Z" transform="translate(-349 -177.27)" fill="#fff"/><g clip-path="url(#clip-path-4)"><circle cx="460.3" cy="353.83" r="28.4" fill="url(#linear-gradient-9)"/></g><path d="M893.5,408.7h-412a8.92,8.92,0,0,1-8.9-8.9v-5.7a8.92,8.92,0,0,1,8.9-8.9h412a8.92,8.92,0,0,1,8.9,8.9v5.7A8.79,8.79,0,0,1,893.5,408.7Z" transform="translate(-349 -177.27)" fill="#fff"/><path id="Path_89" data-name="Path 89" d="M617.4,469c-2,7.7-2.5,9.7,3.1,15.6s9.1,8.6,6.9,15.2-3.8,9.1-5.3,14.4-2.1,10.4-10.9,5.8-16.6-4.9-15.8-11.8,1.4-10.8-1.5-13.7-5.6-5.6-3.4-12.7a144.2,144.2,0,0,1,5.5-14.4l2.5,2.8s-5.2,6.9-5.2,14.4,5.2,5.8,5.2,11.5-1,8.4-.8,13.1,7.6,6.1,13,7.6,6.8,3,8.5-1,3.1-9.9,5.1-15.5,1.2-6.7-3.8-11.7-7.3-9.1-7.3-13.8a21.73,21.73,0,0,1,1.7-8.4Z" transform="translate(-349 -177.27)" fill="#100e26"/><path id="Path_95" data-name="Path 95" d="M547.2,407l-7.2-6.8H517.2s-5.6,1.2-6.4-4.8a79.35,79.35,0,0,1,0-14.5s-.8-5.1,5.1-5.4,24.6,0,24.6,0,3.5,0,4.5,5.9,3.4,24.9,3.4,24.9S548.6,408.3,547.2,407Z" transform="translate(-349 -177.27)" fill="#302d4f"/><path id="Path_96" data-name="Path 96" d="M527.6,384.4s1.6-3.4,5-2.3,3.6,5.4,0,9.7-4.4,4.6-6.8,2.3-9.1-9-5.6-12.2a4.32,4.32,0,0,1,6.2.2A5.67,5.67,0,0,1,527.6,384.4Z" transform="translate(-349 -177.27)" fill="#fafafa"/><path id="Path_100" data-name="Path 100" d="M580.4,447.1a145.39,145.39,0,0,1-1,21.9c-.8,6-5.9,7.8-11.9,6.8s-13.1-4.7-16.9-14.3-1.6-19.7,13.2-22.2S580.4,447.1,580.4,447.1Z" transform="translate(-349 -177.27)" fill="#f0aa89"/><path id="Path_101" data-name="Path 101" d="M595.9,463.7v-3.4a5.12,5.12,0,0,1,5.1-5.1h11.4a5.12,5.12,0,0,1,5.1,5.1h0v8.6l-4.3,1.2H595.9v-6.4Z" transform="translate(-349 -177.27)" fill="#100e26"/><path id="Path_102" data-name="Path 102" d="M599.2,457.5h8.1l1.5,9h-8.1Z" transform="translate(-349 -177.27)" fill="#1f82ab"/><path id="Path_103" data-name="Path 103" d="M603.2,463.7c-1.5,3.5-2.7,6.2-2.8,8.8s-3.6,5.2-6.7,9.1S580.5,497,580.5,497l-8.9-8.4s4.8-2.2,12.5-8.9,9.2-9.3,9.6-13a11.3,11.3,0,0,1,2.3-5.9v2.9l2.5,2.5S599.4,463.2,603.2,463.7Z" transform="translate(-349 -177.27)" fill="#f0aa89"/><circle id="Ellipse_9" data-name="Ellipse 9" cx="264.1" cy="282.43" r="1.6" fill="#1f82ab"/><path id="Path_104" data-name="Path 104" d="M536.5,580.1c-.5,10.2,1.1,26.9,0,40.7s-2.1,23.8.4,43.2,4.2,25.6,2.4,31-1.6,12.5-1.6,12.5h13.1V697c0-5.6-.8-9.4,1.5-27.6a335.13,335.13,0,0,0,2.6-42.4c0-8.4,2-25.6,3.3-35.3S536.5,580.1,536.5,580.1Z" transform="translate(-349 -177.27)" fill="#f0aa89"/><path id="Path_105" data-name="Path 105" d="M564.9,602.1c3.1,11.5,5.4,18.9,5.4,25.6s.3,16.3,7.5,33,11.9,25.6,12,31.5-.8,10.1,0,15.3H611s1.5-4-3.3-7.5-7.2-3.2-7.6-8.3-5.4-34.4-8.4-47.9-5.4-21.8-6.3-41.7S564.9,602.1,564.9,602.1Z" transform="translate(-349 -177.27)" fill="#f0aa89"/><path id="Path_106" data-name="Path 106" d="M536.7,711.6h15.1l1.5-5.8h-18Z" transform="translate(-349 -177.27)" fill="#302d4f"/><path id="Path_107" data-name="Path 107" d="M589.6,711.6h22.8l1.2-5.8H588Z" transform="translate(-349 -177.27)" fill="#302d4f"/><path id="Path_108" data-name="Path 108" d="M602.3,696.6v12.1h4v-9.9C606.3,698.9,605.4,693.9,602.3,696.6Z" transform="translate(-349 -177.27)" fill="#302d4f"/><path id="Path_109" data-name="Path 109" d="M543.1,534.3c-3.8,11.8-8.7,22.5-11.5,39.7a137.66,137.66,0,0,0-2,29.4S538,609,558,608l1.7-17.4,4.2,17.5s18.9.4,29.7-7.3c0,0-4.4-22.5-8.3-38.1a130.85,130.85,0,0,0-11.1-28.4Z" transform="translate(-349 -177.27)" fill="#8398b8"/><path id="Path_110" data-name="Path 110" d="M586.4,499c5.8,6.1,11.9,11.9,11.9,11.9s2.7-5.1,6.3-13.4,6.1-14.9,6.3-17.8-1.2-16-1.2-16,3.6-.3,4.4,4l3.3-5.4v-1.5s3.1-.7,3.1,4.4-2.5,14.6-2.5,14.6-2.5,14.5-4.4,24.2-3.8,21.1-10.4,21.1-17.7-7.4-26.5-15.9C576.8,509.2,573.7,498.6,586.4,499Z" transform="translate(-349 -177.27)" fill="#f0aa89"/><path id="Path_111" data-name="Path 111" d="M564.9,482.6c10.2,1.8,19.4,9.8,25.6,18.5a25.35,25.35,0,0,1-12.8,12s-2,14.1-3.6,21.2c0,0-18.5,5.6-31,0,0,0-3.1-18.9-3.8-27.3S546.2,477.2,564.9,482.6Z" transform="translate(-349 -177.27)" fill="#a4afda"/><path id="Path_113" data-name="Path 113" d="M568,428.8c-5.9-.7-6.9-1.8-12,2s-7.2,2.6-9.2,5.1-4.1,5.5-3.1,11.1-.8,5.1-2.6,8.2-3.6,4.9-3.3,9.9,2.3,5.7,1.5,9.8-.8,4.1-1.6,8.7-4.6,4.1-3.3,9.1,2.6,4.7,1.5,9.3-2,7.2,1.8,9.9,2,1.8,4.9,6.4,3.1,2.6,8.4,4.6,5.1-2.8,7.9-3.6,6.4-.8,9-4.6,4.4-4.4,3.6-9.2-2.8-6.9.2-10.2,3.4-7.2,1.1-11.8-2.3-7.5.3-11,4.6-4.7,4.6-10.3c0,0-4.9-.7-4.9-4.5s4.6-2.6,4.9,0c0,0-1.1-2.8,3.6-5.5s5.1-8.1,1.8-10.9-5.4-5.4-6.1-8.4C576.1,429.9,571.9,429.3,568,428.8Z" transform="translate(-349 -177.27)" fill="#302d4f"/><path id="Path_129" data-name="Path 129" d="M557.7,572.5c.9,10,2,18.1,2,18.1a66.1,66.1,0,0,1-3.6-18.1,56.23,56.23,0,0,1-9.2-.9l11.5-.4Z" transform="translate(-349 -177.27)" fill="#196787"/><path id="Path_97" data-name="Path 97" d="M788.9,266.4l7.2-6.8h22.7s5.6,1.2,6.4-4.8a79.35,79.35,0,0,0,0-14.5s.8-5.1-5.1-5.4-24.6,0-24.6,0-3.5,0-4.5,5.9-3.4,24.9-3.4,24.9S787.5,267.8,788.9,266.4Z" transform="translate(-349 -177.27)" fill="#9bb4da"/><path id="Path_99" data-name="Path 99" d="M808.6,243.8s-1.6-3.4-5-2.3-3.6,5.4,0,9.7,4.4,4.6,6.8,2.3,9.1-9,5.6-12.2a4.32,4.32,0,0,0-6.2.2A3.47,3.47,0,0,0,808.6,243.8Z" transform="translate(-349 -177.27)" fill="#fafafa"/><path id="Path_112" data-name="Path 112" d="M730,210.1c-5.4,0-7.6,1.1-11.3,3.6s-4.9,3.8-7.2,4.4-5.9,1.1-7.2,5.6-4.1,4.9-6.1,6.9a13.66,13.66,0,0,0-4.1,12.8c1.2,6.4-.8,7.2-1.8,10.9s-.3,7.3,2.3,9.1,2.3,3.6,3.1,7.9,3.3,13.3,17.1,13.3,11.5.3,23.3,2.8,12.5-.8,19.2-1.5,7.9-3.1,10.7-9,4.4-6.7,7.6-10.5,3.1-9.9,0-15.6-2-8.2-3.8-10.5-4.9-2.8-4.9-8.2-.5-8.4-5.4-12-4.1-4.9-8.7-7.2-5.1-1.1-10.5-.5C737.1,212.9,738.9,210.1,730,210.1Z" transform="translate(-349 -177.27)" fill="#302d4f"/><path id="Path_114" data-name="Path 114" d="M733,235.4c-3.1,4.6-6.7,3.3-9.9,6.9S720,251,720,252.5c0,0-1.2-2.6-3.1,0s2,7.5,3.3,4.9c0,0-.5,5.4,1.8,9s6.1,6.7,11,6.7,11.3-4.1,13.3-13.8c0,0,2,.3,3.1-3.3s-1.1-5.4-2.8-4.1a4.77,4.77,0,0,1-2.8-4.1c-.3-3.1,1.2-5.6-2.3-7.5C737.8,238.4,734.1,238.5,733,235.4Z" transform="translate(-349 -177.27)" fill="#f0aa89"/><path id="Path_115" data-name="Path 115" d="M737.1,269.6c-.8,2.8.4,7.5,4.3,7.5s6.9-3.5,13.1-11.7,7.6-9.4,8.4-11.1a127.36,127.36,0,0,1,8.2-14.9c4.6-7.5,9.1-12.9,9.1-15.2s-.8-4.8-.8-7.1a9.18,9.18,0,0,1,1.7-4.6V205c0-4.3,3.1-4.3,3.3-1.2s0,8.3,0,8.3.5,2.3,1.5-2,1-7.1,2.8-7.1,2.1,3.3,1,5.8a9.47,9.47,0,0,0-1,4.4s3.4,1.7,1.7,6.1-4.5,5.4-5.9,10.4-4.7,15.8-7.6,20.8-4.8,4.8-6.1,9.9-8.6,16.5-12.4,22.1-5,4.3-5.8,10.7-1.3,16.4-5.4,19.4-34.7-4.4-34.7-4.4a116.59,116.59,0,0,1-5.6,10.4c-3.9,6.6-4.3,4-5.6,9.4s-10.9,18-14.6,21.7-4,2.8-5,4.8-6.3,7.9-6.3,7.9-2-1.5-1-5.6a35.17,35.17,0,0,1-5.6.4c-5,.2-8.1.8-5.8-1.5s8.8-3.5,13.4-4.8,5.6-7.5,9.9-18.2,7.6-20.3,11.1-30.7,8.1-21.7,16.3-23,9.1-.5,11.1-4a10.56,10.56,0,0,0,1.5-6.7Z" transform="translate(-349 -177.27)" fill="#f0aa89"/><path id="Path_116" data-name="Path 116" d="M708.3,350.2c-1,5.6,1.3,16.2,3.6,27.6s1.2,17,.8,23.8-2.7,17.3-.5,20.8,9.4,7,11.9,6.7.8-7.7,0-17.1,1.2-11.5,2.5-19.5,5.3-29.6,5.3-40.5-6.7-15.2-13.8-12.3S708.3,350.2,708.3,350.2Z" transform="translate(-349 -177.27)" fill="#f0aa89"/><path id="Path_117" data-name="Path 117" d="M733.6,372.8c.5,8.4,1.5,20.7,1.3,26.9s-1.7,12.1-1.3,24.2,4,29.7,4,36.8-2.5,16.3,0,25.5,5,3,7.1,0,5-5.9,4.4-12.6-2.7-8.4-1.2-19,3.5-23.6,3.6-36.7.8-17.5,1.2-24,1.3-28.8,2.1-38.4S733.6,372.8,733.6,372.8Z" transform="translate(-349 -177.27)" fill="#f0aa89"/><ellipse id="Ellipse_10" data-name="Ellipse 10" cx="390" cy="78.53" rx="1.2" ry="2.1"/><ellipse id="Ellipse_11" data-name="Ellipse 11" cx="378.3" cy="77.63" rx="1.2" ry="2.1"/><path id="Path_118" data-name="Path 118" d="M728.6,264h8.7s-.1,2.8-4.4,2.8S728.6,264,728.6,264Z" transform="translate(-349 -177.27)" fill="#fafafa"/><path id="Path_119" data-name="Path 119" d="M736.3,248.6l.4,1.2a14,14,0,0,1,2.9,0,16,16,0,0,1,2.1.5s-1.2-1.8-2.7-2A13.12,13.12,0,0,0,736.3,248.6Z" transform="translate(-349 -177.27)"/><path id="Path_120" data-name="Path 120" d="M730,248.6l-.4,1.2a14,14,0,0,0-2.9,0,16,16,0,0,0-2.1.5s1.2-1.8,2.7-2A12.41,12.41,0,0,1,730,248.6Z" transform="translate(-349 -177.27)"/><path id="Path_121" data-name="Path 121" d="M713.1,277.5s-1,5.4-2,13.2-.4,14.4,1.2,17.5,5,9.9,2.7,15.9-14.4,25.9-9.8,34.2c0,0,.5,2.7,3.3-2.1s7.3-9.4,13.2-7.1,8.1,4.3,9.2,13.2-1,18.5,5.6,20.2,11.4,1.5,15.4-2.3,2.8-6.7,4.4-16.5,1-16.2-3.8-25.5-7.5-10.6-6.6-16.7a48,48,0,0,1,2.1-9.1s3-2.5,3.6-9.1l.5-6.5s-10.2,2-17-.6-12.7-7.4-13.5-20.2Z" transform="translate(-349 -177.27)" fill="url(#linear-gradient-10)"/><path id="Path_123" data-name="Path 123" d="M769.8,327.6l7.2-6.8h22.8s5.6,1.2,6.4-4.8a79.35,79.35,0,0,0,0-14.5s.8-5.1-5.1-5.4-24.6,0-24.6,0-3.5,0-4.5,5.9-3.4,24.9-3.4,24.9S768.5,328.9,769.8,327.6Z" transform="translate(-349 -177.27)" fill="#6675b3"/><circle id="Ellipse_13" data-name="Ellipse 13" cx="439.6" cy="131.43" r="8.8" fill="none" stroke="#fafafa" stroke-miterlimit="10" stroke-width="2.08"/><path id="Path_124" data-name="Path 124" d="M785.8,308.2l2.8,3.3,4.5-5.5" transform="translate(-349 -177.27)" fill="none" stroke="#fafafa" stroke-miterlimit="10" stroke-width="2.08"/><path id="Path_125" data-name="Path 125" d="M704.3,208.9l-7.2-6.8H674.3s-5.6,1.2-6.4-4.8a79.35,79.35,0,0,1,0-14.5s-.8-5.1,5.1-5.4,24.6,0,24.6,0,3.5,0,4.5,5.9,3.4,24.9,3.4,24.9S705.7,210.2,704.3,208.9Z" transform="translate(-349 -177.27)" fill="url(#linear-gradient-11)"/><circle id="Ellipse_14" data-name="Ellipse 14" cx="335" cy="12.53" r="8.8" fill="none" stroke="#fafafa" stroke-miterlimit="10" stroke-width="1.78"/><path id="Path_126" data-name="Path 126" d="M686.7,185.9c-.4-1.2-1.5-1.5-3.6,0s-3.2,3.5-.5,3.6,6-.6,5.9,1.8-7.9,3.1-8.7,0" transform="translate(-349 -177.27)" fill="none" stroke="#fafafa" stroke-miterlimit="10" stroke-width="1.78"/><line id="Line_1" data-name="Line 1" x1="335" y1="6.23" x2="335" y2="18.53" fill="none" stroke="#fafafa" stroke-miterlimit="10" stroke-width="1.78"/><path id="Path_130" data-name="Path 130" d="M748,312.4c-4.3,2-9.3.3-11.2-1C736.8,311.4,743.2,313.4,748,312.4Z" transform="translate(-349 -177.27)" fill="url(#linear-gradient-12)"/><path id="Path_131" data-name="Path 131" d="M713.6,310.9c2.6.9,5.4,2,11.8,0A37.66,37.66,0,0,1,713.6,310.9Z" transform="translate(-349 -177.27)" fill="url(#linear-gradient-13)"/></svg>