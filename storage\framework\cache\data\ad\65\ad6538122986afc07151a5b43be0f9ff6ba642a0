1756655419O:23:"TCG\Voyager\Models\Menu":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:5:"menus";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:1;s:4:"name";s:5:"admin";s:10:"created_at";s:19:"2021-08-07 18:52:09";s:10:"updated_at";s:19:"2021-08-07 18:52:09";}s:11:" * original";a:4:{s:2:"id";i:1;s:4:"name";s:5:"admin";s:10:"created_at";s:19:"2021-08-07 18:52:09";s:10:"updated_at";s:19:"2021-08-07 18:52:09";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"parent_items";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:12:{i:0;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:1;s:7:"menu_id";i:1;s:5:"title";s:9:"Dashboard";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-home";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:1;s:10:"created_at";s:19:"2021-08-07 18:52:09";s:10:"updated_at";s:19:"2022-06-24 13:14:52";s:5:"route";s:17:"voyager.dashboard";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:1;s:7:"menu_id";i:1;s:5:"title";s:9:"Dashboard";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-home";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:1;s:10:"created_at";s:19:"2021-08-07 18:52:09";s:10:"updated_at";s:19:"2022-06-24 13:14:52";s:5:"route";s:17:"voyager.dashboard";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:1;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:2;s:7:"menu_id";i:1;s:5:"title";s:5:"Media";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-images";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:2;s:10:"created_at";s:19:"2021-08-07 18:52:09";s:10:"updated_at";s:19:"2021-08-08 22:32:02";s:5:"route";s:19:"voyager.media.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:2;s:7:"menu_id";i:1;s:5:"title";s:5:"Media";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-images";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:2;s:10:"created_at";s:19:"2021-08-07 18:52:09";s:10:"updated_at";s:19:"2021-08-08 22:32:02";s:5:"route";s:19:"voyager.media.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:2;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:10;s:7:"menu_id";i:1;s:5:"title";s:8:"Settings";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:16:"voyager-settings";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:12;s:10:"created_at";s:19:"2021-08-07 18:52:09";s:10:"updated_at";s:19:"2024-08-07 17:34:31";s:5:"route";s:22:"voyager.settings.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:10;s:7:"menu_id";i:1;s:5:"title";s:8:"Settings";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:16:"voyager-settings";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:12;s:10:"created_at";s:19:"2021-08-07 18:52:09";s:10:"updated_at";s:19:"2024-08-07 17:34:31";s:5:"route";s:22:"voyager.settings.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:3;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:26;s:7:"menu_id";i:1;s:5:"title";s:10:"User lists";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-list";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:4;s:10:"created_at";s:19:"2021-08-07 20:54:21";s:10:"updated_at";s:19:"2021-09-30 11:21:46";s:5:"route";N;s:10:"parameters";s:0:"";}s:11:" * original";a:13:{s:2:"id";i:26;s:7:"menu_id";i:1;s:5:"title";s:10:"User lists";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-list";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:4;s:10:"created_at";s:19:"2021-08-07 20:54:21";s:10:"updated_at";s:19:"2021-09-30 11:21:46";s:5:"route";N;s:10:"parameters";s:0:"";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:4;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:27;s:7:"menu_id";i:1;s:5:"title";s:5:"Money";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-dollar";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:7;s:10:"created_at";s:19:"2021-08-07 20:55:37";s:10:"updated_at";s:19:"2023-06-21 15:02:27";s:5:"route";N;s:10:"parameters";s:0:"";}s:11:" * original";a:13:{s:2:"id";i:27;s:7:"menu_id";i:1;s:5:"title";s:5:"Money";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-dollar";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:7;s:10:"created_at";s:19:"2021-08-07 20:55:37";s:10:"updated_at";s:19:"2023-06-21 15:02:27";s:5:"route";N;s:10:"parameters";s:0:"";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:6:{i:0;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:19;s:7:"menu_id";i:1;s:5:"title";s:13:"Subscriptions";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:20:"voyager-credit-cards";s:5:"color";N;s:9:"parent_id";i:27;s:5:"order";i:1;s:10:"created_at";s:19:"2021-08-07 20:25:32";s:10:"updated_at";s:19:"2021-08-07 20:55:55";s:5:"route";s:27:"voyager.subscriptions.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:19;s:7:"menu_id";i:1;s:5:"title";s:13:"Subscriptions";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:20:"voyager-credit-cards";s:5:"color";N;s:9:"parent_id";i:27;s:5:"order";i:1;s:10:"created_at";s:19:"2021-08-07 20:25:32";s:10:"updated_at";s:19:"2021-08-07 20:55:55";s:5:"route";s:27:"voyager.subscriptions.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:1;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:20;s:7:"menu_id";i:1;s:5:"title";s:12:"Transactions";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-dollar";s:5:"color";N;s:9:"parent_id";i:27;s:5:"order";i:2;s:10:"created_at";s:19:"2021-08-07 20:26:33";s:10:"updated_at";s:19:"2021-08-07 20:55:55";s:5:"route";s:26:"voyager.transactions.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:20;s:7:"menu_id";i:1;s:5:"title";s:12:"Transactions";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-dollar";s:5:"color";N;s:9:"parent_id";i:27;s:5:"order";i:2;s:10:"created_at";s:19:"2021-08-07 20:26:33";s:10:"updated_at";s:19:"2021-08-07 20:55:55";s:5:"route";s:26:"voyager.transactions.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:2;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:25;s:7:"menu_id";i:1;s:5:"title";s:11:"Withdrawals";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:16:"voyager-calendar";s:5:"color";N;s:9:"parent_id";i:27;s:5:"order";i:3;s:10:"created_at";s:19:"2021-08-07 20:51:14";s:10:"updated_at";s:19:"2021-08-07 20:55:53";s:5:"route";s:25:"voyager.withdrawals.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:25;s:7:"menu_id";i:1;s:5:"title";s:11:"Withdrawals";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:16:"voyager-calendar";s:5:"color";N;s:9:"parent_id";i:27;s:5:"order";i:3;s:10:"created_at";s:19:"2021-08-07 20:51:14";s:10:"updated_at";s:19:"2021-08-07 20:55:53";s:5:"route";s:25:"voyager.withdrawals.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:3;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:38;s:7:"menu_id";i:1;s:5:"title";s:16:"Payment Requests";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:19:"voyager-window-list";s:5:"color";s:7:"#000000";s:9:"parent_id";i:27;s:5:"order";i:4;s:10:"created_at";s:19:"2022-02-06 16:23:24";s:10:"updated_at";s:19:"2022-02-06 16:25:11";s:5:"route";s:30:"voyager.payment-requests.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:38;s:7:"menu_id";i:1;s:5:"title";s:16:"Payment Requests";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:19:"voyager-window-list";s:5:"color";s:7:"#000000";s:9:"parent_id";i:27;s:5:"order";i:4;s:10:"created_at";s:19:"2022-02-06 16:23:24";s:10:"updated_at";s:19:"2022-02-06 16:25:11";s:5:"route";s:30:"voyager.payment-requests.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:4;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:39;s:7:"menu_id";i:1;s:5:"title";s:8:"Invoices";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:15:"voyager-receipt";s:5:"color";N;s:9:"parent_id";i:27;s:5:"order";i:5;s:10:"created_at";s:19:"2022-08-04 19:06:47";s:10:"updated_at";s:19:"2022-08-04 19:07:16";s:5:"route";s:22:"voyager.invoices.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:39;s:7:"menu_id";i:1;s:5:"title";s:8:"Invoices";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:15:"voyager-receipt";s:5:"color";N;s:9:"parent_id";i:27;s:5:"order";i:5;s:10:"created_at";s:19:"2022-08-04 19:06:47";s:10:"updated_at";s:19:"2022-08-04 19:07:16";s:5:"route";s:22:"voyager.invoices.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:5;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:46;s:7:"menu_id";i:1;s:5:"title";s:9:"Referrals";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-group";s:5:"color";s:7:"#000000";s:9:"parent_id";i:27;s:5:"order";i:6;s:10:"created_at";s:19:"2023-06-21 16:26:04";s:10:"updated_at";s:19:"2023-06-21 16:31:29";s:5:"route";s:21:"voyager.rewards.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:46;s:7:"menu_id";i:1;s:5:"title";s:9:"Referrals";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-group";s:5:"color";s:7:"#000000";s:9:"parent_id";i:27;s:5:"order";i:6;s:10:"created_at";s:19:"2023-06-21 16:26:04";s:10:"updated_at";s:19:"2023-06-21 16:31:29";s:5:"route";s:21:"voyager.rewards.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:5;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:28;s:7:"menu_id";i:1;s:5:"title";s:5:"Posts";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-images";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:5;s:10:"created_at";s:19:"2021-08-07 20:57:03";s:10:"updated_at";s:19:"2021-09-30 11:21:46";s:5:"route";N;s:10:"parameters";s:0:"";}s:11:" * original";a:13:{s:2:"id";i:28;s:7:"menu_id";i:1;s:5:"title";s:5:"Posts";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-images";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:5;s:10:"created_at";s:19:"2021-08-07 20:57:03";s:10:"updated_at";s:19:"2021-09-30 11:21:46";s:5:"route";N;s:10:"parameters";s:0:"";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:4:{i:0;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:17;s:7:"menu_id";i:1;s:5:"title";s:5:"Posts";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-images";s:5:"color";N;s:9:"parent_id";i:28;s:5:"order";i:1;s:10:"created_at";s:19:"2021-08-07 20:22:37";s:10:"updated_at";s:19:"2021-08-07 20:58:22";s:5:"route";s:24:"voyager.user-posts.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:17;s:7:"menu_id";i:1;s:5:"title";s:5:"Posts";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-images";s:5:"color";N;s:9:"parent_id";i:28;s:5:"order";i:1;s:10:"created_at";s:19:"2021-08-07 20:22:37";s:10:"updated_at";s:19:"2021-08-07 20:58:22";s:5:"route";s:24:"voyager.user-posts.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:1;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:14;s:7:"menu_id";i:1;s:5:"title";s:11:"Attachments";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:17:"voyager-paperclip";s:5:"color";s:7:"#000000";s:9:"parent_id";i:28;s:5:"order";i:2;s:10:"created_at";s:19:"2021-08-07 20:16:55";s:10:"updated_at";s:19:"2022-02-01 15:41:20";s:5:"route";s:25:"voyager.attachments.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:14;s:7:"menu_id";i:1;s:5:"title";s:11:"Attachments";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:17:"voyager-paperclip";s:5:"color";s:7:"#000000";s:9:"parent_id";i:28;s:5:"order";i:2;s:10:"created_at";s:19:"2021-08-07 20:16:55";s:10:"updated_at";s:19:"2022-02-01 15:41:20";s:5:"route";s:25:"voyager.attachments.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:2;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:16;s:7:"menu_id";i:1;s:5:"title";s:13:"Post Comments";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-bubble";s:5:"color";N;s:9:"parent_id";i:28;s:5:"order";i:3;s:10:"created_at";s:19:"2021-08-07 20:20:55";s:10:"updated_at";s:19:"2022-02-01 15:41:20";s:5:"route";s:27:"voyager.post-comments.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:16;s:7:"menu_id";i:1;s:5:"title";s:13:"Post Comments";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-bubble";s:5:"color";N;s:9:"parent_id";i:28;s:5:"order";i:3;s:10:"created_at";s:19:"2021-08-07 20:20:55";s:10:"updated_at";s:19:"2022-02-01 15:41:20";s:5:"route";s:27:"voyager.post-comments.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:3;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:21;s:7:"menu_id";i:1;s:5:"title";s:14:"User Bookmarks";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:16:"voyager-bookmark";s:5:"color";N;s:9:"parent_id";i:28;s:5:"order";i:4;s:10:"created_at";s:19:"2021-08-07 20:27:47";s:10:"updated_at";s:19:"2022-02-01 15:41:20";s:5:"route";s:28:"voyager.user-bookmarks.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:21;s:7:"menu_id";i:1;s:5:"title";s:14:"User Bookmarks";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:16:"voyager-bookmark";s:5:"color";N;s:9:"parent_id";i:28;s:5:"order";i:4;s:10:"created_at";s:19:"2021-08-07 20:27:47";s:10:"updated_at";s:19:"2022-02-01 15:41:20";s:5:"route";s:28:"voyager.user-bookmarks.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:6;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:29;s:7:"menu_id";i:1;s:5:"title";s:5:"Users";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-person";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:3;s:10:"created_at";s:19:"2021-08-07 20:58:37";s:10:"updated_at";s:19:"2021-08-08 22:32:02";s:5:"route";N;s:10:"parameters";s:0:"";}s:11:" * original";a:13:{s:2:"id";i:29;s:7:"menu_id";i:1;s:5:"title";s:5:"Users";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-person";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:3;s:10:"created_at";s:19:"2021-08-07 20:58:37";s:10:"updated_at";s:19:"2021-08-08 22:32:02";s:5:"route";N;s:10:"parameters";s:0:"";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:11:{i:0;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:3;s:7:"menu_id";i:1;s:5:"title";s:5:"Users";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-person";s:5:"color";N;s:9:"parent_id";i:29;s:5:"order";i:1;s:10:"created_at";s:19:"2021-08-07 18:52:09";s:10:"updated_at";s:19:"2021-08-07 20:59:55";s:5:"route";s:19:"voyager.users.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:3;s:7:"menu_id";i:1;s:5:"title";s:5:"Users";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-person";s:5:"color";N;s:9:"parent_id";i:29;s:5:"order";i:1;s:10:"created_at";s:19:"2021-08-07 18:52:09";s:10:"updated_at";s:19:"2021-08-07 20:59:55";s:5:"route";s:19:"voyager.users.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:1;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:34;s:7:"menu_id";i:1;s:5:"title";s:15:"Identity Checks";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-check";s:5:"color";s:7:"#000000";s:9:"parent_id";i:29;s:5:"order";i:2;s:10:"created_at";s:19:"2021-10-20 16:11:44";s:10:"updated_at";s:19:"2021-10-20 16:21:40";s:5:"route";s:27:"voyager.user-verifies.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:34;s:7:"menu_id";i:1;s:5:"title";s:15:"Identity Checks";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-check";s:5:"color";s:7:"#000000";s:9:"parent_id";i:29;s:5:"order";i:2;s:10:"created_at";s:19:"2021-10-20 16:11:44";s:10:"updated_at";s:19:"2021-10-20 16:21:40";s:5:"route";s:27:"voyager.user-verifies.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:2;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:4;s:7:"menu_id";i:1;s:5:"title";s:5:"Roles";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-lock";s:5:"color";N;s:9:"parent_id";i:29;s:5:"order";i:3;s:10:"created_at";s:19:"2021-08-07 18:52:09";s:10:"updated_at";s:19:"2021-10-20 16:20:11";s:5:"route";s:19:"voyager.roles.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:4;s:7:"menu_id";i:1;s:5:"title";s:5:"Roles";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-lock";s:5:"color";N;s:9:"parent_id";i:29;s:5:"order";i:3;s:10:"created_at";s:19:"2021-08-07 18:52:09";s:10:"updated_at";s:19:"2021-10-20 16:20:11";s:5:"route";s:19:"voyager.roles.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:3;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:12;s:7:"menu_id";i:1;s:5:"title";s:7:"Wallets";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-wallet";s:5:"color";s:7:"#000000";s:9:"parent_id";i:29;s:5:"order";i:4;s:10:"created_at";s:19:"2021-08-07 19:37:16";s:10:"updated_at";s:19:"2021-10-20 16:20:11";s:5:"route";s:21:"voyager.wallets.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:12;s:7:"menu_id";i:1;s:5:"title";s:7:"Wallets";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-wallet";s:5:"color";s:7:"#000000";s:9:"parent_id";i:29;s:5:"order";i:4;s:10:"created_at";s:19:"2021-08-07 19:37:16";s:10:"updated_at";s:19:"2021-10-20 16:20:11";s:5:"route";s:21:"voyager.wallets.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:4;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:15;s:7:"menu_id";i:1;s:5:"title";s:13:"Notifications";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-bell";s:5:"color";N;s:9:"parent_id";i:29;s:5:"order";i:5;s:10:"created_at";s:19:"2021-08-07 20:19:11";s:10:"updated_at";s:19:"2021-10-20 16:20:11";s:5:"route";s:27:"voyager.notifications.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:15;s:7:"menu_id";i:1;s:5:"title";s:13:"Notifications";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-bell";s:5:"color";N;s:9:"parent_id";i:29;s:5:"order";i:5;s:10:"created_at";s:19:"2021-08-07 20:19:11";s:10:"updated_at";s:19:"2021-10-20 16:20:11";s:5:"route";s:27:"voyager.notifications.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:5;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:24;s:7:"menu_id";i:1;s:5:"title";s:8:"Messages";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-chat";s:5:"color";s:7:"#000000";s:9:"parent_id";i:29;s:5:"order";i:6;s:10:"created_at";s:19:"2021-08-07 20:42:32";s:10:"updated_at";s:19:"2021-10-20 16:20:11";s:5:"route";s:27:"voyager.user-messages.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:24;s:7:"menu_id";i:1;s:5:"title";s:8:"Messages";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-chat";s:5:"color";s:7:"#000000";s:9:"parent_id";i:29;s:5:"order";i:6;s:10:"created_at";s:19:"2021-08-07 20:42:32";s:10:"updated_at";s:19:"2021-10-20 16:20:11";s:5:"route";s:27:"voyager.user-messages.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:6;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:18;s:7:"menu_id";i:1;s:5:"title";s:9:"Reactions";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:19:"voyager-bubble-hear";s:5:"color";N;s:9:"parent_id";i:29;s:5:"order";i:7;s:10:"created_at";s:19:"2021-08-07 20:24:58";s:10:"updated_at";s:19:"2021-10-20 16:20:11";s:5:"route";s:23:"voyager.reactions.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:18;s:7:"menu_id";i:1;s:5:"title";s:9:"Reactions";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:19:"voyager-bubble-hear";s:5:"color";N;s:9:"parent_id";i:29;s:5:"order";i:7;s:10:"created_at";s:19:"2021-08-07 20:24:58";s:10:"updated_at";s:19:"2021-10-20 16:20:11";s:5:"route";s:23:"voyager.reactions.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:7;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:22;s:7:"menu_id";i:1;s:5:"title";s:5:"Lists";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-list";s:5:"color";s:7:"#000000";s:9:"parent_id";i:29;s:5:"order";i:8;s:10:"created_at";s:19:"2021-08-07 20:28:45";s:10:"updated_at";s:19:"2021-10-20 16:20:11";s:5:"route";s:24:"voyager.user-lists.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:22;s:7:"menu_id";i:1;s:5:"title";s:5:"Lists";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-list";s:5:"color";s:7:"#000000";s:9:"parent_id";i:29;s:5:"order";i:8;s:10:"created_at";s:19:"2021-08-07 20:28:45";s:10:"updated_at";s:19:"2021-10-20 16:20:11";s:5:"route";s:24:"voyager.user-lists.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:8;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:23;s:7:"menu_id";i:1;s:5:"title";s:12:"List Members";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-people";s:5:"color";s:7:"#000000";s:9:"parent_id";i:29;s:5:"order";i:9;s:10:"created_at";s:19:"2021-08-07 20:29:07";s:10:"updated_at";s:19:"2021-10-20 16:19:58";s:5:"route";s:31:"voyager.user-list-members.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:23;s:7:"menu_id";i:1;s:5:"title";s:12:"List Members";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-people";s:5:"color";s:7:"#000000";s:9:"parent_id";i:29;s:5:"order";i:9;s:10:"created_at";s:19:"2021-08-07 20:29:07";s:10:"updated_at";s:19:"2021-10-20 16:19:58";s:5:"route";s:31:"voyager.user-list-members.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:9;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:35;s:7:"menu_id";i:1;s:5:"title";s:12:"User Reports";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:11:"voyager-eye";s:5:"color";s:7:"#000000";s:9:"parent_id";i:29;s:5:"order";i:10;s:10:"created_at";s:19:"2021-11-05 11:32:40";s:10:"updated_at";s:19:"2022-02-01 15:41:22";s:5:"route";s:26:"voyager.user-reports.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:35;s:7:"menu_id";i:1;s:5:"title";s:12:"User Reports";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:11:"voyager-eye";s:5:"color";s:7:"#000000";s:9:"parent_id";i:29;s:5:"order";i:10;s:10:"created_at";s:19:"2021-11-05 11:32:40";s:10:"updated_at";s:19:"2022-02-01 15:41:22";s:5:"route";s:26:"voyager.user-reports.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:10;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:37;s:7:"menu_id";i:1;s:5:"title";s:14:"Featured Users";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-star";s:5:"color";s:7:"#000000";s:9:"parent_id";i:29;s:5:"order";i:11;s:10:"created_at";s:19:"2022-02-01 15:00:11";s:10:"updated_at";s:19:"2022-02-01 15:41:54";s:5:"route";s:28:"voyager.featured-users.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:37;s:7:"menu_id";i:1;s:5:"title";s:14:"Featured Users";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-star";s:5:"color";s:7:"#000000";s:9:"parent_id";i:29;s:5:"order";i:11;s:10:"created_at";s:19:"2022-02-01 15:00:11";s:10:"updated_at";s:19:"2022-02-01 15:41:54";s:5:"route";s:28:"voyager.featured-users.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:7;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:32;s:7:"menu_id";i:1;s:5:"title";s:5:"Pages";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-news";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:9;s:10:"created_at";s:19:"2021-09-29 19:43:27";s:10:"updated_at";s:19:"2023-06-21 15:02:27";s:5:"route";s:26:"voyager.custom-pages.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:32;s:7:"menu_id";i:1;s:5:"title";s:5:"Pages";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-news";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:9;s:10:"created_at";s:19:"2021-09-29 19:43:27";s:10:"updated_at";s:19:"2023-06-21 15:02:27";s:5:"route";s:26:"voyager.custom-pages.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:8;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:33;s:7:"menu_id";i:1;s:5:"title";s:5:"Taxes";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:19:"voyager-credit-card";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:8;s:10:"created_at";s:19:"2021-09-30 11:25:21";s:10:"updated_at";s:19:"2023-06-21 15:02:27";s:5:"route";N;s:10:"parameters";s:0:"";}s:11:" * original";a:13:{s:2:"id";i:33;s:7:"menu_id";i:1;s:5:"title";s:5:"Taxes";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:19:"voyager-credit-card";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:8;s:10:"created_at";s:19:"2021-09-30 11:25:21";s:10:"updated_at";s:19:"2023-06-21 15:02:27";s:5:"route";N;s:10:"parameters";s:0:"";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:2:{i:0;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:31;s:7:"menu_id";i:1;s:5:"title";s:5:"Taxes";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:19:"voyager-credit-card";s:5:"color";s:7:"#000000";s:9:"parent_id";i:33;s:5:"order";i:1;s:10:"created_at";s:19:"2021-09-21 18:11:55";s:10:"updated_at";s:19:"2021-09-30 11:26:53";s:5:"route";s:19:"voyager.taxes.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:31;s:7:"menu_id";i:1;s:5:"title";s:5:"Taxes";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:19:"voyager-credit-card";s:5:"color";s:7:"#000000";s:9:"parent_id";i:33;s:5:"order";i:1;s:10:"created_at";s:19:"2021-09-21 18:11:55";s:10:"updated_at";s:19:"2021-09-30 11:26:53";s:5:"route";s:19:"voyager.taxes.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:1;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:30;s:7:"menu_id";i:1;s:5:"title";s:9:"Countries";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:16:"voyager-location";s:5:"color";s:7:"#000000";s:9:"parent_id";i:33;s:5:"order";i:2;s:10:"created_at";s:19:"2021-09-21 18:10:16";s:10:"updated_at";s:19:"2021-09-30 11:26:53";s:5:"route";s:23:"voyager.countries.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:30;s:7:"menu_id";i:1;s:5:"title";s:9:"Countries";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:16:"voyager-location";s:5:"color";s:7:"#000000";s:9:"parent_id";i:33;s:5:"order";i:2;s:10:"created_at";s:19:"2021-09-21 18:10:16";s:10:"updated_at";s:19:"2021-09-30 11:26:53";s:5:"route";s:23:"voyager.countries.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:9;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:36;s:7:"menu_id";i:1;s:5:"title";s:16:"Contact Messages";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-book";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:10;s:10:"created_at";s:19:"2021-11-19 18:11:34";s:10:"updated_at";s:19:"2024-08-07 17:37:08";s:5:"route";s:30:"voyager.contact-messages.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:36;s:7:"menu_id";i:1;s:5:"title";s:16:"Contact Messages";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-book";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:10;s:10:"created_at";s:19:"2021-11-19 18:11:34";s:10:"updated_at";s:19:"2024-08-07 17:37:08";s:5:"route";s:30:"voyager.contact-messages.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:10;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:42;s:7:"menu_id";i:1;s:5:"title";s:7:"Streams";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-video";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:6;s:10:"created_at";s:19:"2023-06-21 14:56:54";s:10:"updated_at";s:19:"2023-06-21 15:02:27";s:5:"route";N;s:10:"parameters";s:0:"";}s:11:" * original";a:13:{s:2:"id";i:42;s:7:"menu_id";i:1;s:5:"title";s:7:"Streams";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-video";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:6;s:10:"created_at";s:19:"2023-06-21 14:56:54";s:10:"updated_at";s:19:"2023-06-21 15:02:27";s:5:"route";N;s:10:"parameters";s:0:"";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:2:{i:0;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:43;s:7:"menu_id";i:1;s:5:"title";s:7:"Streams";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-video";s:5:"color";s:7:"#000000";s:9:"parent_id";i:42;s:5:"order";i:1;s:10:"created_at";s:19:"2023-06-21 15:01:44";s:10:"updated_at";s:19:"2023-06-21 15:03:03";s:5:"route";s:21:"voyager.streams.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:43;s:7:"menu_id";i:1;s:5:"title";s:7:"Streams";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-video";s:5:"color";s:7:"#000000";s:9:"parent_id";i:42;s:5:"order";i:1;s:10:"created_at";s:19:"2023-06-21 15:01:44";s:10:"updated_at";s:19:"2023-06-21 15:03:03";s:5:"route";s:21:"voyager.streams.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:1;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:41;s:7:"menu_id";i:1;s:5:"title";s:15:"Stream Messages";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-chat";s:5:"color";N;s:9:"parent_id";i:42;s:5:"order";i:2;s:10:"created_at";s:19:"2023-06-21 14:48:56";s:10:"updated_at";s:19:"2023-06-21 15:02:27";s:5:"route";s:29:"voyager.stream-messages.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:41;s:7:"menu_id";i:1;s:5:"title";s:15:"Stream Messages";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-chat";s:5:"color";N;s:9:"parent_id";i:42;s:5:"order";i:2;s:10:"created_at";s:19:"2023-06-21 14:48:56";s:10:"updated_at";s:19:"2023-06-21 15:02:27";s:5:"route";s:29:"voyager.stream-messages.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}i:11;O:27:"TCG\Voyager\Models\MenuItem":32:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"menu_items";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:47;s:7:"menu_id";i:1;s:5:"title";s:13:"Announcements";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:17:"voyager-megaphone";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:11;s:10:"created_at";s:19:"2024-08-07 17:27:57";s:10:"updated_at";s:19:"2024-08-07 17:37:08";s:5:"route";s:34:"voyager.global-announcements.index";s:10:"parameters";s:4:"null";}s:11:" * original";a:13:{s:2:"id";i:47;s:7:"menu_id";i:1;s:5:"title";s:13:"Announcements";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:17:"voyager-megaphone";s:5:"color";s:7:"#000000";s:9:"parent_id";N;s:5:"order";i:11;s:10:"created_at";s:19:"2024-08-07 17:27:57";s:10:"updated_at";s:19:"2024-08-07 17:37:08";s:5:"route";s:34:"voyager.global-announcements.index";s:10:"parameters";s:4:"null";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:15:" * translatable";a:1:{i:0;s:5:"title";}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:0:{}}