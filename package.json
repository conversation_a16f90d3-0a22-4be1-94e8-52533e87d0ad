{"name": "qdev-justfans", "private": true, "scripts": {"prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "dependencies": {"@selectize/selectize": "^0.13.6", "@simonwep/pickr": "^1.8.2", "animate.css": "^4.1.1", "bootstrap": "^4.6.2", "chart.js": "^2.8.0", "cookieconsent": "^3.1.1", "dropzone": "^5.9.2", "easymde": "^2.18.0", "easyqrcodejs": "^4.4.10", "ionicons": "^5.5.3", "jquery": "^3.0.0", "jquery-backstretch": "^2.1.18", "malihu-custom-scrollbar-plugin": "^3.1.5", "photoswipe": "^4.1.3", "placeholder-loading": "^0.5.0", "popper.js": "^1.16.1", "pusher-js": "^7.0.3", "pusher-js-auth": "^4.0.0", "swiper": "^6.7.1", "video.js": "^7.21.6", "videojs-contrib-quality-levels": "^2.2.1", "videojs-http-source-selector": "^1.1.6", "wow.js": "^1.2.2", "xss": "^1.0.14"}, "devDependencies": {"@laylazi/bootstrap-rtl-scss": "^4.6.2-1", "cross-env": "^5.2.1", "eslint": "^7.32.0", "laravel-mix": "^4.0.7", "resolve-url-loader": "^3.1.0", "sass": "^1.32.5", "sass-loader": "^7.1.0"}}