
body::selection {
  color: $selection-color;
  background: $selection-bg;
}

.h-pill-primary:hover{
    background-color: lighten($gray-800-alt,10%)!important;
    color:lighten($primary-alt,15%)!important;
}

.EasyMDEContainer {
    background-color: #3B3B3B!important;
}

.CodeMirror, .editor-preview {
    color: $input-color-alt!important;
    border-color: $gray-600-alt !important;
    background-color: #3B3B3B!important;

}

.cm-s-easymde .CodeMirror-cursor {
    border-color: $gray-600-alt !important;
}

.editor-toolbar{
    border-color: $gray-600-alt !important;
}

.editor-toolbar button i {
    color: $white !important;
}

.editor-toolbar button.active, .editor-toolbar button:hover {
    background-color: transparent !important; // Fixes toolbar buttons from appearing as white when hovered or selected
}

.editor-preview pre {
    background-color: #333 !important; // This fixes code blocks in Markdown appearing with a white background
}
