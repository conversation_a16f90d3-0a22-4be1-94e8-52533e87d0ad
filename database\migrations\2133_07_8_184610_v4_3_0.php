<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class V430 extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        DB::table('settings')->insert(
            array(
                array (
                    'key' => 'security.allow_geo_blocking',
                    'display_name' => 'Allow users to be able to geoblock their profiles',
                    'value' => NULL,
                    'details' => '{
"on" : "On",
"off" : "Off",
"checked" : false,
"description": "If enabled, users will be able to disallow certain countries to access their content."
}',
                    'type' => 'checkbox',
                    'order' => 80,
                    'group' => 'Security',
                ),
            )
        );

        DB::table('settings')->insert(
            array(
                array (
                    'key' => 'security.abstract_api_key',
                    'display_name' => 'IP geolocation API key (Abstract API)',
                    'value' => '',
                    'type' => 'text',
                    'order' => 81,
                    'group' => 'Security'
                )
            )
        );


        if (Schema::hasTable('countries')) {
            Schema::table('countries', function (Blueprint $table) {
                $table->string('country_code')->after('name')->nullable();
                $table->string('phone_code')->after('country_code')->nullable();
                $table->index('country_code');
                $table->index('phone_code');
            });
        }

        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                $table->boolean('enable_geoblocking')->nullable()->after('enable_2fa');
            });
        }

        // Countries fix
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('countries')->truncate();
        DB::statement("
        INSERT INTO `countries` (`id`, `name`, `country_code`, `phone_code`, `created_at`, `updated_at`) VALUES
	    (1, 'All', NULL, NULL, '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (2, 'Afghanistan', 'AF', '93', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (3, 'Albania', 'AL', '355', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (4, 'Algeria', 'DZ', '213', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (5, 'American Samoa', 'AS', '1684', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (6, 'Andorra', 'AD', '376', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (7, 'Angola', 'AO', '244', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (8, 'Anguilla', 'AI', '1264', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (9, 'Antarctica', 'AQ', '672', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (10, 'Antigua and Barbuda', 'AG', '1268', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (11, 'Argentina', 'AR', '54', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (12, 'Armenia', 'AM', '374', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (13, 'Aruba', 'AW', '297', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (14, 'Australia', 'AU', '61', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (15, 'Austria', 'AT', '43', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (16, 'Azerbaijan', 'AZ', '994', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (17, 'Bahamas (the)', 'BS', '1242', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (18, 'Bahrain', 'BH', '973', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (19, 'Bangladesh', 'BD', '880', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (20, 'Barbados', 'BB', '1246', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (21, 'Belarus', 'BY', '375', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (22, 'Belgium', 'BE', '32', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (23, 'Belize', 'BZ', '501', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (24, 'Benin', 'BJ', '229', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (25, 'Bermuda', 'BM', '1441', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (26, 'Bhutan', 'BT', '975', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (27, 'Bolivia (Plurinational State of)', 'BO', '591', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (28, 'Bonaire, Sint Eustatius and Saba', 'BQ', '599', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (29, 'Bosnia and Herzegovina', 'BA', '387', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (30, 'Botswana', 'BW', '267', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (31, 'Bouvet Island', 'BV', '55', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (32, 'Brazil', 'BR', '55', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (33, 'British Indian Ocean Territory (the)', 'IO', '246', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (34, 'Brunei Darussalam', 'BN', '673', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (35, 'Bulgaria', 'BG', '359', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (36, 'Burkina Faso', 'BF', '226', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (37, 'Burundi', 'BI', '257', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (38, 'Cabo Verde', 'CV', '238', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (39, 'Cambodia', 'KH', '855', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (40, 'Cameroon', 'CM', '237', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (41, 'Canada', 'CA', '1', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (42, 'Cayman Islands (the)', 'KY', '1345', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (43, 'Central African Republic (the)', 'CF', '236', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (44, 'Chad', 'TD', '235', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (45, 'Chile', 'CL', '56', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (46, 'China', 'CN', '86', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (47, 'Christmas Island', 'CX', '61', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (48, 'Cocos (Keeling) Islands (the)', 'CC', '672', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (49, 'Colombia', 'CO', '57', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (50, 'Comoros (the)', 'KM', '269', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (51, 'Congo (the Democratic Republic of the)', 'CG', '242', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (52, 'Congo (the)', 'CG', '242', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (53, 'Cook Islands (the)', 'CK', '682', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (54, 'Costa Rica', 'CR', '506', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (55, 'Croatia', 'HR', '385', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (56, 'Cuba', 'CU', '53', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (57, 'Curaçao', 'CW', '599', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (58, 'Cyprus', 'CY', '357', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (59, 'Czechia', 'CZ', '420', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (60, 'Côte d\'Ivoire', 'CI', '225', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (61, 'Denmark', 'DK', '45', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (62, 'Djibouti', 'DJ', '253', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (63, 'Dominica', 'DM', '1767', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (64, 'Dominican Republic (the)', 'DO', '1809', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (65, 'Ecuador', 'EC', '593', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (66, 'Egypt', 'EG', '20', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (67, 'El Salvador', 'SV', '503', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (68, 'Equatorial Guinea', 'GQ', '240', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (69, 'Eritrea', 'ER', '291', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (70, 'Estonia', 'EE', '372', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (71, 'Eswatini', 'SWZ', '268', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (72, 'Ethiopia', 'ET', '251', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (73, 'Falkland Islands (the) [Malvinas]', 'FK', '500', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (74, 'Faroe Islands (the)', 'FO', '298', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (75, 'Fiji', 'FJ', '679', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (76, 'Finland', 'FI', '358', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (77, 'France', 'FR', '33', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (78, 'French Guiana', 'GF', '594', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (79, 'French Polynesia', 'PF', '689', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (80, 'French Southern Territories (the)', 'GF', '594', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (81, 'Gabon', 'GA', '241', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (82, 'Gambia (the)', 'GM', '220', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (83, 'Georgia', 'GE', '995', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (84, 'Germany', 'DE', '49', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (85, 'Ghana', 'GH', '233', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (86, 'Gibraltar', 'GI', '350', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (87, 'Greece', 'GR', '30', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (88, 'Greenland', 'GL', '299', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (89, 'Grenada', 'GD', '1473', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (90, 'Guadeloupe', 'GP', '590', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (91, 'Guam', 'GU', '1671', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (92, 'Guatemala', 'GT', '502', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (93, 'Guernsey', 'GG', '44', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (94, 'Guinea', 'GN', '224', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (95, 'Guinea-Bissau', 'GW', '245', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (96, 'Guyana', 'GY', '592', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (97, 'Haiti', 'HT', '509', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (98, 'Heard Island and McDonald Islands', 'HM', '0', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (99, 'Holy See (the)', 'VA', '39', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (100, 'Honduras', 'HN', '504', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (101, 'Hong Kong', 'HK', '852', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (102, 'Hungary', 'HU', '36', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (103, 'Iceland', 'IS', '354', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (104, 'India', 'IN', '91', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (105, 'Indonesia', 'ID', '62', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (106, 'Iran (Islamic Republic of)', 'IR', '98', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (107, 'Iraq', 'IQ', '964', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (108, 'Ireland', 'IE', '353', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (109, 'Isle of Man', 'IM', '44', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (110, 'Israel', 'IL', '972', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (111, 'Italy', 'IT', '39', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (112, 'Jamaica', 'JM', '1876', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (113, 'Japan', 'JP', '81', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (114, 'Jersey', 'JE', '44', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (115, 'Jordan', 'JO', '962', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (116, 'Kazakhstan', 'KZ', '7', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (117, 'Kenya', 'KE', '254', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (118, 'Kiribati', 'KI', '686', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (119, 'Korea (the Democratic People\'s Republic of)', 'KP', '850', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (120, 'Korea (the Republic of)', 'KP', '850', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (121, 'Kuwait', 'KW', '965', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (122, 'Kyrgyzstan', 'KG', '996', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (123, 'Lao People\'s Democratic Republic (the)', 'LA', '856', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (124, 'Latvia', 'LV', '371', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (125, 'Lebanon', 'LB', '961', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (126, 'Lesotho', 'LS', '266', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (127, 'Liberia', 'LR', '231', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (128, 'Libya', 'LY', '218', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (129, 'Liechtenstein', 'LI', '423', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (130, 'Lithuania', 'LT', '370', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (131, 'Luxembourg', 'LU', '352', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (132, 'Macao', 'MO', '853', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (133, 'Madagascar', 'MG', '261', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (134, 'Malawi', 'MW', '265', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (135, 'Malaysia', 'MY', '60', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (136, 'Maldives', 'MV', '960', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (137, 'Mali', 'ML', '223', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (138, 'Malta', 'MT', '356', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (139, 'Marshall Islands (the)', 'MH', '692', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (140, 'Martinique', 'MQ', '596', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (141, 'Mauritania', 'MR', '222', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (142, 'Mauritius', 'MU', '230', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (143, 'Mayotte', 'YT', '269', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (144, 'Mexico', 'MX', '52', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (145, 'Micronesia (Federated States of)', 'FM', '691', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (146, 'Moldova (the Republic of)', 'MD', '373', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (147, 'Monaco', 'MC', '377', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (148, 'Mongolia', 'MN', '976', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (149, 'Montenegro', 'ME', '382', '2022-04-14 20:35:50', '2022-04-14 20:35:50'),
	    (150, 'Montserrat', 'MS', '1664', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (151, 'Morocco', 'MA', '212', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (152, 'Mozambique', 'MZ', '258', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (153, 'Myanmar', 'MM', '95', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (154, 'Namibia', 'NA', '264', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (155, 'Nauru', 'NR', '674', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (156, 'Nepal', 'NP', '977', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (157, 'Netherlands (the)', 'NL', '31', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (158, 'New Caledonia', 'NC', '687', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (159, 'New Zealand', 'NZ', '64', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (160, 'Nicaragua', 'NI', '505', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (161, 'Niger (the)', 'NE', '227', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (162, 'Nigeria', 'NG', '234', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (163, 'Niue', 'NU', '683', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (164, 'Norfolk Island', 'NF', '672', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (165, 'Northern Mariana Islands (the)', 'MP', '1670', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (166, 'Norway', 'NO', '47', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (167, 'Oman', 'OM', '968', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (168, 'Pakistan', 'PK', '92', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (169, 'Palau', 'PW', '680', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (170, 'Palestine, State of', 'PH', '63', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (171, 'Panama', 'PA', '507', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (172, 'Papua New Guinea', 'PG', '675', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (173, 'Paraguay', 'PY', '595', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (174, 'Peru', 'PE', '51', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (175, 'Philippines (the)', 'PH', '63', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (176, 'Pitcairn', 'PN', '64', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (177, 'Poland', 'PL', '48', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (178, 'Portugal', 'PT', '351', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (179, 'Puerto Rico', 'PR', '1787', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (180, 'Qatar', 'QA', '974', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (181, 'Republic of North Macedonia', 'MK', '389', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (182, 'Romania', 'RO', '40', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (183, 'Russian Federation (the)', 'RU', '70', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (184, 'Rwanda', 'RW', '250', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (185, 'Réunion', 'RE', '262', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (186, 'Saint Barthélemy', 'BL', '590', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (187, 'Saint Helena, Ascension and Tristan da Cunha', 'SH', '290', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (188, 'Saint Kitts and Nevis', 'KN', '1869', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (189, 'Saint Lucia', 'LC', '1758', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (190, 'Saint Martin (French part)', 'MF', '590', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (191, 'Saint Pierre and Miquelon', 'PM', '508', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (192, 'Saint Vincent and the Grenadines', 'VC', '1784', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (193, 'Samoa', 'WS', '684', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (194, 'San Marino', 'SM', '378', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (195, 'Sao Tome and Principe', 'ST', '239', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (196, 'Saudi Arabia', 'SA', '966', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (197, 'Senegal', 'SN', '221', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (198, 'Serbia', 'RS', '381', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (199, 'Seychelles', 'SC', '248', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (200, 'Sierra Leone', 'SL', '232', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (201, 'Singapore', 'SG', '65', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (202, 'Sint Maarten (Dutch part)', 'SX', '1', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (203, 'Slovakia', 'SK', '421', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (204, 'Slovenia', 'SI', '386', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (205, 'Solomon Islands', 'SB', '677', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (206, 'Somalia', 'SO', '252', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (207, 'South Africa', 'ZA', '27', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (208, 'South Georgia and the South Sandwich Islands', 'GS', '500', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (209, 'South Sudan', 'SS', '211', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (210, 'Spain', 'ES', '34', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (211, 'Sri Lanka', 'LK', '94', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (212, 'Sudan (the)', 'SS', '211', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (213, 'Suriname', 'SR', '597', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (214, 'Svalbard and Jan Mayen', 'SJ', '47', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (215, 'Sweden', 'SE', '46', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (216, 'Switzerland', 'CH', '41', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (217, 'Syrian Arab Republic', 'SY', '963', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (218, 'Taiwan', 'TW', '886', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (219, 'Tajikistan', 'TJ', '992', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (220, 'Tanzania, United Republic of', 'TZ', '255', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (221, 'Thailand', 'TH', '66', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (222, 'Timor-Leste', 'TL', '670', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (223, 'Togo', 'TG', '228', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (224, 'Tokelau', 'TK', '690', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (225, 'Tonga', 'TO', '676', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (226, 'Trinidad and Tobago', 'TT', '1868', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (227, 'Tunisia', 'TN', '216', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (228, 'Turkey', 'TR', '90', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (229, 'Turkmenistan', 'TM', '7370', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (230, 'Turks and Caicos Islands (the)', 'TC', '1649', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (231, 'Tuvalu', 'TV', '688', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (232, 'Uganda', 'UG', '256', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (233, 'Ukraine', 'UA', '380', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (234, 'United Arab Emirates (the)', 'AE', '971', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (235, 'United Kingdom of Great Britain and Northern Ireland (the)', 'GB', '44', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (236, 'United States Minor Outlying Islands (the)', 'UM', '1', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (237, 'United States of America (the)', 'US', '1', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (238, 'Uruguay', 'UY', '598', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (239, 'Uzbekistan', 'UZ', '998', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (240, 'Vanuatu', 'VU', '678', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (241, 'Venezuela (Bolivarian Republic of)', 'VE', '58', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (242, 'Viet Nam', 'VN', '84', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (243, 'Virgin Islands (British)', 'VG', '1284', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (244, 'Virgin Islands (U.S.)', 'VI', '1340', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (245, 'Wallis and Futuna', 'WF', '681', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (246, 'Western Sahara', 'EH', '212', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (247, 'Yemen', 'YE', '967', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (248, 'Zambia', 'ZM', '260', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (249, 'Zimbabwe', 'ZW', '263', '2022-04-14 20:35:51', '2022-04-14 20:35:51'),
	    (250, 'Åland Islands', 'AX', '358', '2022-04-14 20:35:51', '2022-04-14 20:35:51');");
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        DB::table('settings')->insert(
            array (
                'key' => 'social-media.telegram_link',
                'display_name' => 'Telegram',
                'value' => NULL,
                'details' => NULL,
                'type' => 'text',
                'order' => 86,
                'group' => 'Social media',
            )
        );

//         TODO: add license key / category

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

        DB::table('settings')
            ->where('key', 'security.allow_geo_blocking')
            ->delete();

        DB::table('settings')
            ->where('key', 'security.abstract_api_key')
            ->delete();

        DB::table('settings')
            ->where('key', 'social-media.telegram_link')
            ->delete();

        Schema::table('countries', function (Blueprint $table) {
            $table->dropColumn('country_code');
            $table->dropColumn('phone_code');
        });

        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('enable_geoblocking');
        });

    }
}
